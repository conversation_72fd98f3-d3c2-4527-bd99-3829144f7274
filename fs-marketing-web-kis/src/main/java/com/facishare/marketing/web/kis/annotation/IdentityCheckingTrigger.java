package com.facishare.marketing.web.kis.annotation;

import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface IdentityCheckingTrigger {
    IdentityCheckTypeEnum[] authScope() default {IdentityCheckTypeEnum.FXIAOKE};
}
