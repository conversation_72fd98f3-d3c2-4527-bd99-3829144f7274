package com.facishare.marketing.provider.dao.card;

import com.facishare.marketing.provider.entity.CardTemplateEntity;
import com.facishare.marketing.provider.entity.enterprisecard.EnterpriseDefaultCardEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/24
 **/
public interface EnterpriseDefaultCardDao {

    @Insert("INSERT INTO enterprise_default_card " +
            "(id, ea, company_name, company_address, trade_code, address_book_default_range, user_id, create_time, update_time, card_template_id,card_hexagon_id,content_type) " +
            "values " +
            "(#{obj.id}, #{obj.ea}, #{obj.companyName}, #{obj.companyAddress}, #{obj.tradeCode}, #{obj.addressBookDefaultRange}, #{obj.userId}, now(), now(), #{obj.cardTemplateId},#{obj.cardHexagonId},#{obj.contentType}) ")
    int insert(@Param("obj") EnterpriseDefaultCardEntity entity);

    @Update("UPDATE enterprise_default_card SET card_template_id=#{cardTemplateId}, update_time=now() WHERE ea=#{ea}")
    int updateCardTplIdByEa(@Param("ea") String ea, @Param("cardTemplateId") String cardTemplateId);

    @Select("SELECT * FROM enterprise_default_card WHERE id = #{id}")
    EnterpriseDefaultCardEntity getById(@Param("id") String id);

    @Select("SELECT * FROM enterprise_default_card WHERE ea = #{ea} order by create_time asc limit 1 ")
    EnterpriseDefaultCardEntity getByEa(@Param("ea") String ea);

    @Delete("delete FROM enterprise_default_card WHERE card_template_id = #{cardTemplateId}")
    int deleteByCardTplId(@Param("cardTemplateId") String cardTemplateId);

    @Select("SELECT * FROM enterprise_default_card WHERE ea = #{ea} and card_template_id = #{cardTemplateId} order by create_time desc limit 1 ")
    EnterpriseDefaultCardEntity queryEnterpriseDefaultCardByEa(@Param("ea") String ea, @Param("cardTemplateId") String cardTemplateId);

}
