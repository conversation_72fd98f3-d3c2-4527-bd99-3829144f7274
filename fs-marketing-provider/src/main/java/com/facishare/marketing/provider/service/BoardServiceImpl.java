/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.annoation.FilterI18n;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.typehandlers.value.IntegerList;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.AddBoardCardArg;
import com.facishare.marketing.api.arg.AddBoardCardListArg;
import com.facishare.marketing.api.arg.AddBoardCardTaskArg;
import com.facishare.marketing.api.arg.AddEnterpriseTemplateArg;
import com.facishare.marketing.api.arg.GetSopInfoArg;
import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.arg.ListBoardActivityArg;
import com.facishare.marketing.api.arg.ListBoardCardActivityArg;
import com.facishare.marketing.api.arg.MoveBoardCardArg;
import com.facishare.marketing.api.arg.OfficeMessageArg;
import com.facishare.marketing.api.arg.UpdateBoardCardArg;
import com.facishare.marketing.api.arg.UpdateBoardCardListArg;
import com.facishare.marketing.api.arg.UpdateBoardCardTaskArg;
import com.facishare.marketing.api.arg.UpdateBoardCardTaskStatusArg;
import com.facishare.marketing.api.arg.UpdateBoardMenuArg;
import com.facishare.marketing.api.arg.UpdateBoardNameArg;
import com.facishare.marketing.api.arg.UpdateBoardTemplateArg;
import com.facishare.marketing.api.arg.UpdateBoardUserArg;
import com.facishare.marketing.api.arg.UpdateBoardVisibleRangeArg;
import com.facishare.marketing.api.data.BoardStatisticData;
import com.facishare.marketing.api.data.BoardTemplateData;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.BoardCardActivityResult;
import com.facishare.marketing.api.result.BoardCardListResult;
import com.facishare.marketing.api.result.BoardCardResult;
import com.facishare.marketing.api.result.BoardCardTaskResult;
import com.facishare.marketing.api.result.BoardListCheckResult;
import com.facishare.marketing.api.result.BoardMenuResult;
import com.facishare.marketing.api.result.BoardResult;
import com.facishare.marketing.api.result.ListBoardCardListResult;
import com.facishare.marketing.api.result.SOPBoardTabResult;
import com.facishare.marketing.api.service.BoardService;
import com.facishare.marketing.api.vo.EnterpriseBoardTemplateVo;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.enums.BoardAssociatedObjectTypeEnum;
import com.facishare.marketing.common.enums.BoardCardActivityActionTypeEnum;
import com.facishare.marketing.common.enums.BoardCardGoalType;
import com.facishare.marketing.common.enums.BoardCardStatus;
import com.facishare.marketing.common.enums.BoardCardTaskStatus;
import com.facishare.marketing.common.enums.BoardCardTypeEnum;
import com.facishare.marketing.common.enums.BoardOperatorTypeEnum;
import com.facishare.marketing.common.enums.BoardTemplateTypeEnum;
import com.facishare.marketing.common.enums.BoardTypeEnum;
import com.facishare.marketing.common.enums.BoardVisibleRangeEnum;
import com.facishare.marketing.common.enums.MarketingSceneType;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.BoardCardActivityContentData;
import com.facishare.marketing.common.typehandlers.value.NestedIdList;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.UrlUtils;
import com.facishare.marketing.provider.dao.BoardCardActivityDao;
import com.facishare.marketing.provider.dao.BoardCardDao;
import com.facishare.marketing.provider.dao.BoardCardListDao;
import com.facishare.marketing.provider.dao.BoardCardTaskDao;
import com.facishare.marketing.provider.dao.BoardDao;
import com.facishare.marketing.provider.dao.BoardToMarketingUserGroupRelationDao;
import com.facishare.marketing.provider.dao.BoardUserDao;
import com.facishare.marketing.provider.dao.DisplayOrderDao;
import com.facishare.marketing.provider.dao.distribution.DistributionPlanDAO;
import com.facishare.marketing.provider.dto.BoardIdToBoardCardNumDTO;
import com.facishare.marketing.provider.dto.BoardIdToBoardCardTaskNumDTO;
import com.facishare.marketing.provider.entity.BoardCardActivityEntity;
import com.facishare.marketing.provider.entity.BoardCardEntity;
import com.facishare.marketing.provider.entity.BoardCardListEntity;
import com.facishare.marketing.provider.entity.BoardCardTaskEntity;
import com.facishare.marketing.provider.entity.BoardEntity;
import com.facishare.marketing.provider.entity.BoardMenuEntity;
import com.facishare.marketing.provider.entity.BoardUserEntity;
import com.facishare.marketing.provider.entity.DisplayOrderEntity;
import com.facishare.marketing.provider.entity.distribution.DistributePlanEntity;
import com.facishare.marketing.provider.manager.BoardManager;
import com.facishare.marketing.provider.manager.DisplayOrderManager;
import com.facishare.marketing.provider.manager.EmployeeMsgSender;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.vo.GetMarketingActivityDetailVo;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("boardService")
@Slf4j
@FilterI18n
public class BoardServiceImpl implements BoardService {
    @Autowired
    private DisplayOrderDao displayOrderDao;
    @Autowired
    private DisplayOrderManager displayOrderManager;
    @Autowired
    private BoardCardListDao boardCardListDao;
    @Autowired
    private BoardCardDao boardCardDao;
    @Autowired
    private BoardCardTaskDao boardCardTaskDao;
    @Autowired
    private BoardCardActivityDao boardCardActivityDao;
    @Autowired
    private BoardDao boardDao;
    @Autowired
    private BoardUserDao boardUserDao;
    @Autowired
    private BoardManager boardManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private MarketingCrmManager marketingCrmManager;
    @Autowired
    private EmployeeMsgSender employeeMsgSender;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private DistributionPlanDAO distributionPlanDAO;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private BoardToMarketingUserGroupRelationDao boardToMarketingUserGroupRelationDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Value("${host}")
    private String host;

    @Override
    public Result<List<EnterpriseBoardTemplateVo>> listBoardTemplate(String ea, Integer fsUserId, Boolean isRandom) {
        List<EnterpriseBoardTemplateVo> list = boardManager.listBoardTemplateData();
        // 随机排序
        if (isRandom != null && isRandom) {
            Collections.shuffle(list);
        }
        return Result.newSuccess(list);
    }

    @Override
    public Result<ListBoardCardListResult> listBoardCardListByBoardId(String ea, Integer fsUserId, String boardId) {
        Preconditions.checkArgument(checkFsUserAuthorization(ea, fsUserId, boardId), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_167));
        List<BoardCardListEntity> boardCardLists = boardCardListDao.listByBoardId(ea, boardId);
        List<BoardCardListResult> boardCardListResults = boardCardLists.stream().map(boardCardList -> {
            BoardCardListResult result = new BoardCardListResult();
            result.setId(boardCardList.getId());
            result.setName(boardCardList.getName());
            result.setCreator(boardCardList.getCreator());
            return result;
        }).collect(Collectors.toList());
        ListBoardCardListResult result = new ListBoardCardListResult();
        if (boardCardListResults.isEmpty()){
            return Result.newSuccess(result);
        }
        DisplayOrderEntity displayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.getCardListDisplayKey(boardId));
        NestedIdList.sortByNestedIds(displayOrder.getDisplayItems(), boardCardListResults, BoardCardListResult::getId);
        result.setBoardCardLists(boardCardListResults);
        result.setDisplayOrderVersion(displayOrder.getVersion());
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> addBoardCardList(String ea, Integer fsUserId, AddBoardCardListArg arg) {
        Preconditions.checkArgument(arg != null && !Strings.isNullOrEmpty(arg.getName()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_189));
        Preconditions.checkArgument(checkFsUserAuthorization(ea, fsUserId, arg.getBoardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_167));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getBoardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_191));
        return Result.newSuccess(boardManager.addBoardCardList(ea, fsUserId, arg.getBoardId(), arg.getName()));
    }

    @Override
    public Result<Boolean> updateBoardCardList(String ea, Integer fsUserId, UpdateBoardCardListArg arg) {
        Preconditions.checkArgument(arg != null && !Strings.isNullOrEmpty(arg.getBoardCardListId()) && !Strings.isNullOrEmpty(arg.getName()), I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_6));
        checkFsUserBoardCardListAuth(ea, fsUserId, arg.getBoardCardListId());

        int updateCount = boardCardListDao.updateBoardCardListName(arg.getBoardCardListId(), ea, arg.getName());
        return Result.newSuccess(updateCount > 0);
    }

    @Override
    public Result<Boolean> deleteBoardCardList(String ea, Integer fsUserId, String boardCardListId) {
        checkFsUserBoardCardListAuth(ea, fsUserId, boardCardListId);
        BoardCardListEntity boardCardList = boardCardListDao.getById(boardCardListId, ea);
        if (boardCardList == null){
            return Result.newSuccess(Boolean.TRUE);
        }
        int deleteCount = boardCardListDao.deleteById(boardCardListId, ea);
        if (deleteCount > 0){
            displayOrderManager.removeFirstNestedId(ea, DisplayOrderConstants.getCardListDisplayKey(boardCardList.getBoardId()), boardCardListId);
            //删除看板列表下的卡片、任务、动态
            List<BoardCardEntity> boardCardEntities = boardCardDao.listByBoardCardListId(ea, boardCardListId);
            for (BoardCardEntity boardCardEntity : boardCardEntities) {
                boardCardTaskDao.batchDeleteBoardCardTaskByBoardCardId(ea, boardCardEntity.getId());
                boardCardActivityDao.batchDeleteBoardCardActivityByBoardCardId(ea, boardCardEntity.getId());
            }
            boardCardDao.batchDeleteBoardCardByBoardCardListId(boardCardListId, ea);
        }
        return Result.newSuccess(Boolean.TRUE);
    }

    @Override
    public Result<String> addBoardCard(String ea, Integer fsUserId, AddBoardCardArg addBoardCardArg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(addBoardCardArg.getBoardCardListId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_227));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(addBoardCardArg.getName()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_189));
        Preconditions.checkArgument(BoardCardTypeEnum.isValid(addBoardCardArg.getType()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_229));
        BoardCardListEntity boardCardList = boardCardListDao.getById(addBoardCardArg.getBoardCardListId(), ea);
        Preconditions.checkArgument(boardCardList != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_231));
        return Result.newSuccess(boardManager.addBoardCard(ea, fsUserId, boardCardList.getBoardId(), addBoardCardArg, false));
    }


    @Override
    public Result<Boolean> updateBoardCard(String ea, Integer fsUserId, UpdateBoardCardArg updateBoardCardArg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(updateBoardCardArg.getBoardCardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_238));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(updateBoardCardArg.getName()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_239));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(updateBoardCardArg.getBoardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_240));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(updateBoardCardArg.getStatus()) && BoardCardStatus.isValid(updateBoardCardArg.getStatus()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_241));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(updateBoardCardArg.getType()) && BoardCardTypeEnum.isValid(updateBoardCardArg.getType()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_229));
        BoardCardEntity existedBoardCard = boardCardDao.getById(updateBoardCardArg.getBoardCardId(), ea);
        if (existedBoardCard == null) {
            existedBoardCard = boardCardDao.getById(updateBoardCardArg.getBoardCardId(), "__SYSTEM");
            Preconditions.checkArgument(existedBoardCard == null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_246));
        }
        Preconditions.checkArgument(existedBoardCard != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_248));
        checkFsUserBoardCardAuth(ea, updateBoardCardArg.getBoardCardId());

        BoardCardEntity boardCardToUpdate = new BoardCardEntity();
        BeanUtils.copyProperties(updateBoardCardArg, boardCardToUpdate, "startTime", "endTime");
        boardCardToUpdate.setId(updateBoardCardArg.getBoardCardId());
        if (updateBoardCardArg.getStartTime() != null){
            boardCardToUpdate.setStartTime(new Date(updateBoardCardArg.getStartTime()));
        }
        if(updateBoardCardArg.getEndTime() != null){
            boardCardToUpdate.setEndTime(new Date(updateBoardCardArg.getEndTime()));
        }
        boardCardToUpdate.setStatus(updateBoardCardArg.getStatus());
        int updateRow = boardCardDao.updateBoardCard(boardCardToUpdate);
        if (updateRow > 0) {
            addBoardCardActivityWhenUpdateBoardCard(ea, fsUserId, updateBoardCardArg, existedBoardCard, boardCardToUpdate);
        }

        // 重置
        BoardCardEntity boardCard = boardCardDao.getNeedResetCheckGoalReachedTaskById(ea, updateBoardCardArg.getBoardCardId());
        if (boardCard != null){
            Map<String, BoardStatisticData> boardCardIdToStatisticDataMap = boardManager.getBoardCardStatisticDataMapByEntities(ea, ImmutableList.of(boardCard));
            BoardStatisticData statisticData = boardCardIdToStatisticDataMap.get(updateBoardCardArg.getBoardCardId());
            if (statisticData == null || statisticData.getGoalValueByGoalType(boardCard.getGoalType()) < boardCard.getGoalValue()){
                boardCardDao.changeBoardCardGoalReachedNotifySentStatus(ea, boardCard.getId(), true, false);
            }
        }

        List<Integer> existedPrincipals = existedBoardCard.getPrincipals() == null ? new ArrayList<>(0) : new ArrayList<>(existedBoardCard.getPrincipals());
        if (updateBoardCardArg.getPrincipals() != null && !updateBoardCardArg.getPrincipals().isEmpty()){
            Set<Integer> principalsToNotify = new HashSet<>(updateBoardCardArg.getPrincipals());
            principalsToNotify.remove(fsUserId);
            existedPrincipals.forEach(principalsToNotify::remove);
            if (!principalsToNotify.isEmpty()){
                boardManager.sendAddAsBoardCardPrincipalNotification(ea, fsUserId, updateBoardCardArg.getBoardCardId(), principalsToNotify);
            }
        }
        return Result.newSuccess(updateRow == 1);
    }

    @Override
    public Result<Boolean> deleteBoardCard(String ea, Integer fsUserId, IdArg idArg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(idArg.getId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_290));
        checkFsUserBoardCardAuth(ea, idArg.getId());
        boolean deleted = boardCardDao.deleteById(idArg.getId(), ea) > 0;
        if (deleted){
            boardCardTaskDao.deleteByBoardCardId(ea, idArg.getId());
            boardCardActivityDao.deleteBoardCardActivityByBoardCardId(ea, idArg.getId());
        }
        return Result.newSuccess(deleted);
    }

    @Override
    public Result<Boolean> moveBoardCard(String ea, Integer fsUserId, MoveBoardCardArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getBoardCardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_302));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getBoardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_240));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getTargetBoardCardListId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_304));
        BoardCardEntity existedBoardCard = boardCardDao.getById(arg.getBoardCardId(), ea);
        Preconditions.checkArgument(existedBoardCard != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_306));
        BoardCardListEntity targetBoardCardList = boardCardListDao.getById(arg.getTargetBoardCardListId(), ea);
        Preconditions.checkArgument(targetBoardCardList != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_308));
        Preconditions.checkArgument(existedBoardCard.getBoardId().equals(targetBoardCardList.getBoardId()), "卡片与目标列表要在同一个看板中");
        checkFsUserBoardCardAuth(ea, arg.getBoardCardId());

        boolean updated = boardCardDao.updateBoardCardListId(ea, arg.getBoardCardId(), arg.getTargetBoardCardListId()) > 0;
        if (updated){
            displayOrderManager.removeFirstNestedId(ea, DisplayOrderConstants.getCardDisplayKey(existedBoardCard.getBoardCardListId()), arg.getBoardCardId());
            displayOrderManager.insertAfterTargetNestedId(ea, DisplayOrderConstants.getCardDisplayKey(arg.getTargetBoardCardListId()), arg.getTargetPreBoardCardId(), arg.getBoardCardId());
            addBoardCardActivityWhenMoveBoardCard(ea, fsUserId, arg.getBoardId(), arg.getBoardCardId(), existedBoardCard.getBoardCardListId(), targetBoardCardList.getId(), targetBoardCardList.getName());
        }
        return Result.newSuccess(updated);
    }

    @Override
    public Result<BoardCardResult> getBoardCardDetailWithBoardNameAndBoardCardListName(String ea, Integer fsUserId, String boardCardId) {
        Result<BoardCardResult> boardCardDetailResult = getBoardCardDetail(ea, fsUserId, boardCardId);
        if (BooleanUtils.isTrue(boardCardDetailResult.isSuccess())) {
            BoardCardResult boardCardDetail = boardCardDetailResult.getData();
            boardCardDetail.setBoardName(boardDao.getBoardNameByBoardId(boardCardDetail.getBoardId(), ea));
            boardCardDetail.setBoardCardListName(boardCardListDao.getBoardCardListNameById(boardCardDetail.getBoardCardListId(), ea));
        }
        return boardCardDetailResult;
    }

    @Override
    public Result<BoardCardResult> getBoardCardDetail(String ea, Integer fsUserId, String boardCardId) {
        BoardCardEntity boardCard = boardCardDao.getById(boardCardId, ea);
        if (boardCard == null) {
            ea = "__SYSTEM";
            boardCard = boardCardDao.getById(boardCardId, ea);
        }
        Preconditions.checkArgument(boardCard != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_248));
        checkFsUserBoardCardAuth(ea, boardCardId);
        BoardCardResult boardCardResult = doConvertBoardEntityToResult(boardCard);
        String finalEa = ea;
        //添加卡片负责人信息
        if (CollectionUtils.isNotEmpty(boardCardResult.getPrincipals())) {
            List<String> executorName = new LinkedList<>();
            IntegerList principals = boardCardResult.getPrincipals();
            if (principals.contains(-10000)) {
                executorName.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
            }
            // 过滤掉 -10000
            List<Integer> collect = principals.stream().filter(principal -> principal != -10000).collect(Collectors.toList());
            Map<Integer, FSEmployeeMsg> employeeInfoByUserIds = fsAddressBookManager.getEmployeeInfoByUserIds(ea, collect, true);
            if (MapUtils.isNotEmpty(employeeInfoByUserIds)) {
                executorName.addAll(employeeInfoByUserIds.values().stream().map(FSEmployeeMsg::getFullName).collect(Collectors.toList()));
            }
            boardCardResult.setPrincipalsName(executorName);
        }

        boardManager.fillStatisticDataToBoardCardResult(ea, ImmutableList.of(boardCardResult));
        boardManager.fillTargetObjectNameToBoardCardResult(ea, ImmutableList.of(boardCardResult));

        List<BoardCardTaskEntity> boardCardTaskEntities = boardCardTaskDao.listByBoardCardId(ea, boardCardId);
        final Map<Integer, FsAddressBookManager.FSEmployeeMsg> boardCardTaskExecutorMsg = getBoardCardTaskExecutorMsg(ea, boardCardTaskEntities);
        List<BoardCardTaskResult> boardCardTaskResults = boardCardTaskEntities.stream().map(boardCardTask -> {
            BoardCardTaskResult boardCardTaskResult = new BoardCardTaskResult();
            BeanUtils.copyProperties(boardCardTask, boardCardTaskResult, "startTime", "endTime");
            if (boardCardTask.getStartTime() != null){
                boardCardTaskResult.setStartTime(boardCardTask.getStartTime().getTime());
            }
            if (boardCardTask.getEndTime() != null){
                boardCardTaskResult.setEndTime(boardCardTask.getEndTime().getTime());
            }
            if (boardCardTaskExecutorMsg != null && !boardCardTaskExecutorMsg.isEmpty()) {
                FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = boardCardTaskExecutorMsg.get(boardCardTask.getExecutor());
                if (fsEmployeeMsg != null) {
                    fsAddressBookManager.buildNPathProfileImage2Url(fsEmployeeMsg, finalEa);
                    boardCardTaskResult.setExecutorName(fsEmployeeMsg.getFullName());
                    boardCardTaskResult.setExecutorAvatar(fsEmployeeMsg.getProfileImage());
                }
            }
            return boardCardTaskResult;
        }).collect(Collectors.toList());
        if (!boardCardTaskResults.isEmpty() && !"__SYSTEM".equals(ea)){
            DisplayOrderEntity displayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.getTaskDisplayKey(boardCardId));
            NestedIdList.sortByNestedIds(displayOrder.getDisplayItems(), boardCardTaskResults, BoardCardTaskResult::getId);
            boardCardResult.setBoardCardTaskListVersion(displayOrder.getVersion());
        }

        boardCardResult.setTaskList(boardCardTaskResults);
        boardCardResult.setLatestBoardCardActivity(boardManager.getLatestBoardCardActivityByBoardCardId(ea, boardCardId));
        return Result.newSuccess(boardCardResult);
    }

    private Map<Integer, FsAddressBookManager.FSEmployeeMsg> getBoardCardTaskExecutorMsg(String ea, List<BoardCardTaskEntity> boardCardTaskEntities) {
        if (CollectionUtils.isEmpty(boardCardTaskEntities)) {
            return new HashMap<>();
        }
        List<Integer> taskExecutor = boardCardTaskEntities.stream()
                .filter(ele -> ele != null && ele.getExecutor() != null)
                .map(BoardCardTaskEntity::getExecutor)
                .collect(Collectors.toList());
        return fsAddressBookManager.getEmployeeInfoByUserIds(ea, taskExecutor, false);
    }

    private BoardCardResult doConvertBoardEntityToResult(BoardCardEntity boardCard) {
        BoardCardResult boardCardResult = new BoardCardResult();
        BeanUtils.copyProperties(boardCard, boardCardResult, "startTime", "endTime");
        if (boardCard.getStartTime() != null){
            boardCardResult.setStartTime(boardCard.getStartTime().getTime());
        }
        if(boardCard.getEndTime() != null){
            boardCardResult.setEndTime(boardCard.getEndTime().getTime());
        }
        return boardCardResult;
    }

    @Override
    public Result<String> addBoardCardTask(String ea, Integer fsUserId, AddBoardCardTaskArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getBoardCardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_412));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getName()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_239));
        BoardCardEntity boardCard = boardCardDao.getById(arg.getBoardCardId(), ea);
        Preconditions.checkArgument(boardCard != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_248));
        checkFsUserBoardCardAuth(ea, arg.getBoardCardId());

        return Result.newSuccess(boardManager.addBoardCardTask(ea, fsUserId, boardCard.getBoardId(), arg, false, true));
    }

    @Override
    public Result<String> updateBoardCardTask(String ea, Integer fsUserId, UpdateBoardCardTaskArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getBoardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_423));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getBoardCardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_424));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_425));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getName()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_189));
        checkFsUserBoardCardTaskAuth(arg.getId());

        if (arg.getStartTime() != null && arg.getEndTime() != null){
            Preconditions.checkArgument(arg.getEndTime() > arg.getStartTime(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_430));
        }
        BoardCardTaskEntity existedBoardCardTask = boardCardTaskDao.getById(arg.getId());
        Preconditions.checkArgument(existedBoardCardTask != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_433));
        BoardCardTaskEntity boardCardTaskToUpdate = new BoardCardTaskEntity();
        BeanUtils.copyProperties(arg, boardCardTaskToUpdate, "startTime", "endTime");
        if (arg.getStartTime() != null){
            boardCardTaskToUpdate.setStartTime(new Date(arg.getStartTime()));
            addBoardCardActivityWhenUpdateTaskStartTime(ea, fsUserId, arg, existedBoardCardTask);
        }
        if (arg.getEndTime() != null){
            boardCardTaskToUpdate.setEndTime(new Date(arg.getEndTime()));
            addBoardCardActivityWhenUpdateTaskEndTime(ea, fsUserId, arg, existedBoardCardTask);
        }
        addBoardCardActivityWhenUpdateBoardCardTaskExecutor(ea, fsUserId, arg, existedBoardCardTask);
        addBoardCardActivityWhenUpdateBoardCardTaskName(ea, String.valueOf(fsUserId), existedBoardCardTask, arg.getName(), BoardOperatorTypeEnum.USER.getOperator());
        boardCardTaskToUpdate.setStatus(BoardCardTaskStatus.NOT_START.getStatus());
        boardCardTaskDao.updateBoardCardTask(ea, boardCardTaskToUpdate);
        boardCardDao.updateBoardCardUpdateTimeWhenUpdateTask(arg.getBoardCardId());

        // 重置发送状态
        if (arg.getStartTime() != null && arg.getStartTime() > System.currentTimeMillis()){
            boardCardTaskDao.changeTaskStartNotifySent(arg.getId(), true, false);
        }
        if (arg.getEndTime() != null && arg.getEndTime() > System.currentTimeMillis()){
            boardCardTaskDao.changeTaskEndNotifySent(arg.getId(), true, false);
        }
        if (arg.getExecutor() != null && !arg.getExecutor().equals(fsUserId) && !arg.getExecutor().equals(existedBoardCardTask.getExecutor())){
            boardManager.sendAddAsBoardCardTaskExecutorNotification(ea, fsUserId, arg.getId(), arg.getExecutor());
        }
        return Result.newSuccess(arg.getId());
    }

    private void addBoardCardActivityWhenUpdateBoardCardTaskName(String ea, String operator, BoardCardTaskEntity existedBoardCardTask, String name, String operatorType) {
        if (name != null && name.equals(existedBoardCardTask.getName())) {
            return ;
        }
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        contentData.setOldBoardCardTaskName(existedBoardCardTask.getName());
        contentData.setBoardCardTaskName(name);
        boardManager.addBoardCardActivity(ea, operator, existedBoardCardTask.getBoardId(), existedBoardCardTask.getBoardCardId(), BoardCardActivityActionTypeEnum.UPDATE_TASK_NAME.getActionType(),
            contentData, false, operatorType);
    }


    @Override
    public Result<Boolean> updateBoardCardTaskStatus(String ea, Integer fsUserId, UpdateBoardCardTaskStatusArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getBoardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_240));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getBoardCardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_478));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getTaskName()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_479));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_425));
        Preconditions.checkArgument(BoardCardTaskStatus.isValid(arg.getStatus()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_481));
        checkFsUserBoardCardTaskAuth(arg.getId());

        String dbStatus = boardCardTaskDao.getStatusById(arg.getId());
        int updateCount = boardCardTaskDao.updateBoardCardTaskStatus(ea, arg.getId(), arg.getStatus());
        if (updateCount > 0) {
            addBoardCardActivityWhenUpdateBoardCardTaskStatus(ea, fsUserId, arg, dbStatus);
            boardCardDao.updateBoardCardUpdateTimeWhenUpdateTask(arg.getBoardCardId());
        }
        return Result.newSuccess(updateCount > 0);
    }

    @Override
    public Result<Boolean> deleteBoardCardTask(String ea, Integer fsUserId, IdArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getId()));
        checkFsUserBoardCardTaskAuth(arg.getId());
        BoardCardTaskEntity boardCardTask = boardCardTaskDao.getById(arg.getId());
        if (boardCardTask == null){
            return Result.newSuccess(true);
        }
        boolean deleted = boardCardTaskDao.deleteById(ea, arg.getId()) > 0;
        if (deleted){
            displayOrderManager.removeFirstNestedId(ea, DisplayOrderConstants.getTaskDisplayKey(boardCardTask.getBoardCardId()), arg.getId());
            addBoardCardActivityWhenDeleteBoardCardTask(ea, fsUserId, boardCardTask);
        }
        return Result.newSuccess(deleted);
    }

    @Override
    public Result<BoardResult> getBoardDetail(String ea, Integer fsUserId, String boardId) {
        BoardEntity board = boardDao.getBoardByBoardId(boardId, ea);
        if (board == null) {
            board = boardDao.getBoardByBoardId(boardId, "__SYSTEM");
            if (board == null) {
                return Result.newError(SHErrorCode.BOARD_NOT_EXIST);
            }
            ea = "__SYSTEM";
        }
        BoardResult boardResult = new BoardResult();
        BeanUtils.copyProperties(board, boardResult, "createTime");
        boardResult.setCreateTime(board.getCreateTime().getTime());

        //如果看板可见范围为private，则查询看板下的可见用户列表
        if (BoardVisibleRangeEnum.PRIVATE_VISIBLE_RANGE.getVisibleRange().equals(board.getVisibleRange())) {
            boardResult.setBoardUsers(boardUserDao.listEmployeeIdByBoardId(ea, boardId));
        }
        List<BoardCardListEntity> listEntities = "__SYSTEM".equals(ea) ? boardCardListDao.listByBoardIdOrderByCreateTime(ea, boardId) : boardCardListDao.listByBoardId(ea, boardId);
        List<BoardCardListResult> boardCardListResults = listEntities.stream().map(boardCardList -> {
            BoardCardListResult boardCardListResult = new BoardCardListResult();
            BeanUtils.copyProperties(boardCardList, boardCardListResult);
            return boardCardListResult;
        }).collect(Collectors.toList());
        if (!boardCardListResults.isEmpty()){
            DisplayOrderEntity cardListDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.getCardListDisplayKey(boardId));
            if (cardListDisplayOrder != null){
                NestedIdList.sortByNestedIds(cardListDisplayOrder.getDisplayItems(), boardCardListResults, BoardCardListResult::getId);
                boardResult.setBoardCardListVersion(cardListDisplayOrder.getVersion());
            }
        }
        boardResult.setBoardCardLists(boardCardListResults);

        List<BoardCardResult> boardCardResults = boardCardDao.listByBoardId(ea, boardId).stream().map(this::doConvertBoardEntityToResult).collect(Collectors.toList());
        boardCardResults.forEach(boardResult::pushBoardCardResult);
        for (BoardCardListResult boardCardList : boardResult.getBoardCardLists()) {
            if (boardCardList.getBoardCards() != null && !boardCardList.getBoardCards().isEmpty()){
                DisplayOrderEntity cardDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.getCardDisplayKey(boardCardList.getId()));
                if (cardDisplayOrder != null){
                    NestedIdList.sortByNestedIds(cardDisplayOrder.getDisplayItems(), boardCardList.getBoardCards(), BoardCardResult::getId);
                }
            }
        }
        boardManager.fillStatisticDataToBoardCardResult(ea, boardCardResults);
        boardManager.fillTargetObjectNameToBoardCardResult(ea, boardCardResults);
        boardManager.fillLatestBoardCardActivityToBoardCardResult(ea, boardCardResults);
        // 看板侧栏设置
        boardManager.fillStatisticDataToBoardTargetResult(ea, boardResult);
        boardManager.fillTargetObjectNameToBoardResult(ea, boardResult);
        boardResult.setMarketingUserGroupIds(boardToMarketingUserGroupRelationDao.queryMarketingUserGroupIds(ea, boardId));

        // 企业模板，判断是否有修改权限
        if (1 == board.getTemplateType()) {
            if ("__SYSTEM".equals(ea)) {
                boardResult.setHaveAuthority(false);
            } else {
                LinkedList<String> boardIdList = new LinkedList<>();
                boardIdList.add(boardId);
                List<BoardUserEntity> boardUserEntities = boardUserDao.listBoardUserEntitiesByEaAndBoardIds(ea, boardIdList, fsUserId);
                // 与fsUserId有关企业模板
                Set<String> fsUserIdRelateWithBoardIds = boardUserEntities.stream().map(BoardUserEntity::getBoardId).collect(Collectors.toSet());
                Boolean haveAuthority = BoardVisibleRangeEnum.PUBLIC_VISIBLE_RANGE.getVisibleRange().equals(board.getVisibleRange()) || fsUserIdRelateWithBoardIds.contains(boardId);
                boardResult.setHaveAuthority(haveAuthority);
            }
        }

        return Result.newSuccess(boardResult);
    }

    @Override
    public Result<String> addBoard(String ea, Integer fsUserId, BoardTemplateData arg) {
        Preconditions.checkArgument(arg != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_580));
        String objectId = arg.getObjectId();
        if (BoardTypeEnum.SOP_TYPE.getType().equals(arg.getType())) {
            Preconditions.checkArgument(StringUtils.isNotEmpty(objectId), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_583));
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, objectId);
            arg.setName(marketingEventData.getName());
        } else {
            Preconditions.checkArgument(StringUtils.isNotEmpty(arg.getName()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_587));
        }
        arg.setVisibleRage(StringUtils.isBlank(arg.getVisibleRage()) ? BoardVisibleRangeEnum.PRIVATE_VISIBLE_RANGE.getVisibleRange() : arg.getVisibleRage());
        arg.setType(StringUtils.isBlank(arg.getType()) ? BoardTypeEnum.DATA_TYPE.getType() : arg.getType());
        // 使用模板创建看板
        String defaultTemplateId = arg.getDefaultTemplateId();
        if (StringUtils.isNotBlank(defaultTemplateId)) {
            Result<BoardResult> boardDetailResult = getBoardDetail(ea, fsUserId, defaultTemplateId);
            BoardResult boardDetail = boardDetailResult.getData();
            if (!boardDetailResult.isSuccess() || boardDetail == null) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            fillBoardCardTasksToBoardResult(ea, boardDetail);
            BoardTemplateData boardTemplateData = boardDetail.toBoardTemplateData();
            boardTemplateData.setName(arg.getName());
            boardTemplateData.setType(arg.getType());
            boardTemplateData.setVisibleRage(BoardTypeEnum.SOP_TYPE.getType().equals(arg.getType()) ? BoardVisibleRangeEnum.PUBLIC_VISIBLE_RANGE.getVisibleRange() : arg.getVisibleRage());
            boardTemplateData.setBoardUserIds(arg.getBoardUserIds());
            boardTemplateData.setMarketingUserGroupIds(arg.getMarketingUserGroupIds());
            boardTemplateData.setDefaultTemplateId(arg.getDefaultTemplateId());
            boardTemplateData.setObjectId(objectId);
            boardTemplateData.setCover(arg.getCover());
            if (StringUtils.isEmpty(boardTemplateData.getCover())) {
                // 从个人模板获得封面
                boardTemplateData.setCover(boardDao.queryCoverFromEnterpriseTemplate(ea, defaultTemplateId));
                // 从系统模板获得封面
                if (StringUtils.isEmpty(boardTemplateData.getCover())) {
                    boardTemplateData.setCover(boardDao.queryCoverFromEnterpriseTemplate("__SYSTEM", defaultTemplateId));
                }
            }
            String boardId = boardManager.addBoardByTemplateData(ea, fsUserId, boardTemplateData, BooleanUtils.isTrue(arg.getIsTemplate()), false);
            return Result.newSuccess(boardId);
        }
        // 用户自定义模板（or系统模板）
        return Result.newSuccess(boardManager.addBoardByTemplateData(ea, fsUserId, arg, BooleanUtils.isTrue(arg.getIsTemplate()), false));
    }

    private boolean fillBoardCardTasksToBoardResult(String ea, BoardResult boardResult) {
        List<String> boardCardIds = boardCardDao.listBoardCardIdsByBoardId(ea, boardResult.getId());
        if (boardCardIds == null || boardCardIds.size() == 0) {
            ea = "__SYSTEM";
            boardCardIds = boardCardDao.listBoardCardIdsByBoardId(ea, boardResult.getId());
        }
        Optional<List<BoardCardTaskEntity>> boardCardTaskEntitiesOptional = boardManager.listBoardCardTaskEntityByBoardCardIds(ea, boardCardIds);
        if (boardCardTaskEntitiesOptional.isPresent()) {
            List<BoardCardTaskEntity> boardCardTaskEntities = boardCardTaskEntitiesOptional.get();
            Map<String, List<BoardCardTaskEntity>> boardCardIdToBoardCardTasks = boardCardTaskEntities.stream().collect(Collectors.groupingBy(BoardCardTaskEntity::getBoardCardId));
            for (BoardCardListResult boardCardList : boardResult.getBoardCardLists()) {
                for (BoardCardResult boardCard : boardCardList.getBoardCards()) {
                    List<BoardCardTaskEntity> taskList = boardCardIdToBoardCardTasks.get(boardCard.getId());
                    if (CollectionUtils.isNotEmpty(taskList)) {
                        List<BoardCardTaskResult> boardCardTaskResults = taskList.stream().map(boardCardTaskEntity -> {
                            BoardCardTaskResult boardCardTaskResult = new BoardCardTaskResult();
                            BeanUtils.copyProperties(boardCardTaskEntity, boardCardTaskResult, "startTime", "endTime");
                            boardCardTaskResult.setStartTime(boardCardTaskEntity.getStartTime() == null ? null : boardCardTaskEntity.getStartTime().getTime());
                            boardCardTaskResult.setEndTime(boardCardTaskEntity.getEndTime() == null ? null : boardCardTaskEntity.getEndTime().getTime());
                            return boardCardTaskResult;
                        }).collect(Collectors.toList());
                        boardCard.setTaskList(boardCardTaskResults);
                    }
                }
            }
        }
        return true;
    }

    @Override
    public Result<List<BoardResult>> listBoardByFsUserId(String ea, Integer fsUserId) {
        //私有看板的id
        List<String> boardIds = boardUserDao.listBoardIdByEmployeeId(ea, fsUserId);
        //添加公开看板的id
        boardIds.addAll(boardDao.listBoardIdsWithPublicVisibleRange(ea));
        if (boardIds.isEmpty()) {
            return Result.newSuccess(new LinkedList<>());
        }
        List<BoardEntity> boards = boardDao.listBoardByIds(boardIds).stream().filter(BoardEntity::notTemplate).collect(Collectors.toList());
        List<BoardIdToBoardCardNumDTO> boardIdToBoardCardNumDTOList = boardCardDao.listBoardCardNumByBoardIds(ea, boardIds);
        Map<String, Integer> boardIdToBoardCardNumMap = boardIdToBoardCardNumDTOList.stream().collect(Collectors.toMap(BoardIdToBoardCardNumDTO::getBoardId, BoardIdToBoardCardNumDTO::getBoardCardNum, (k1, k2) -> k1));
        List<BoardIdToBoardCardTaskNumDTO> boardIdToBoardCardTaskNumList = boardCardTaskDao.listBoardCardTaskNumByBoardIds(ea, boardIds);
        Map<String, Integer> boardIdToBoardCardTaskNumMap = boardIdToBoardCardTaskNumList.stream().collect(Collectors.toMap(BoardIdToBoardCardTaskNumDTO::getBoardId, BoardIdToBoardCardTaskNumDTO::getBoardCardTaskNum, (k1, k2) -> k1));
        List<BoardResult> boardResults = boards.stream().map(boardEntity -> {
            BoardResult boardResult = new BoardResult();
            BeanUtils.copyProperties(boardEntity, boardResult, "createTime");
            boardResult.setCreateTime(boardEntity.getCreateTime() == null ? null : boardEntity.getCreateTime().getTime());
            boardResult.setCreatorId(boardEntity.getCreator());
//            boardResult.setCover(boardEntity.getCover());
            boardResult.setBoardCardNum(boardIdToBoardCardNumMap.getOrDefault(boardEntity.getId(), 0));
            boardResult.setBoardCardTaskNum(boardIdToBoardCardTaskNumMap.getOrDefault(boardEntity.getId(), 0));
//            boardManager.fillStatisticDataToBoardTargetResult(ea, boardResult);
            if (boardResult.getGoalValue() == null && StringUtils.isNotEmpty(boardResult.getGoalType())) {
                boardResult.setGoalValue(0);
            }
            return boardResult;
        }).collect(Collectors.toList());
        boardManager.batchFillStatisticDataToBoardTargetResults(ea, boardResults);
        return Result.newSuccess(boardResults);
    }

    /**
     * 根据看板id删除看板，级联删除对应的用户、看板列表
     */
    @Override
    public Result<Boolean> deleteBoardByBoardId(String ea, Integer fsUserId, String boardId) {
        //查看当前用户是否在可见范围内，仅可见范围内可以删除看板
        Preconditions.checkState(checkFsUserAuthorization(ea, fsUserId, boardId), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_691));
        //删除看板
        boolean deleteResult = boardDao.deleteBoardById(boardId);
        if (deleteResult) {
            // 添加动态
            addBoardCardActivityWhenDeleteBoard(ea, fsUserId, boardId);
            //级联删除看板列表
            boardCardListDao.batchDeleteBoardCardListsByBoardId(ea, boardId);
            //级联删除看板卡片
            boardCardDao.batchDeleteBoardCardByBoardId(boardId, ea);
            //级联删除看板任务
            boardCardTaskDao.batchDeleteBoardCardTaskByBoardId(ea, boardId);
            //级联删除看板动态
            boardCardActivityDao.batchDeleteBoardCardActivityByBoardId(ea, boardId);
            //级联删除看板用户
            boardUserDao.deleteBoardUsersByBoardId(ea, boardId);
        }
        return Result.newSuccess(deleteResult);
    }

    @Override
    public Result<Boolean> updateBoardNameByBoardId(String ea, Integer fsUserId, UpdateBoardNameArg arg) {
        Preconditions.checkArgument(arg != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_713));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getId()) && !Strings.isNullOrEmpty(arg.getName()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_714));
        //查看当前用户是否在可见范围内，仅可见范围内可以更新看板
        if (!checkFsUserAuthorization(ea, fsUserId, arg.getId())) {
            return Result.newError(SHErrorCode.USER_DOSE_NOT_HAVE_PERMISSION);
        }
        int row = boardDao.updateBoardNameByBoardId(arg.getId(), arg.getName());
        return Result.newSuccess(row > 0);
    }

    @Override
    public Result<Boolean> updateBoardUserByBoardId(String ea, Integer fsUserId, UpdateBoardUserArg arg) {
        Preconditions.checkArgument(arg != null && arg.getBoardId() != null, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_6));
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(arg.getBoardUserIds()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_726));
        //最新的看板人员
        List<Integer> newBoardUsers = arg.getBoardUserIds();
        //数据库的看板人员
        List<Integer> dbBoardUsers = boardUserDao.listEmployeeIdByBoardId(ea, arg.getBoardId());
        //更新的看板人员与数据库的看板人员交集
        List<Integer> intersectionBoardUser = new LinkedList<>(dbBoardUsers);
        intersectionBoardUser.retainAll(newBoardUsers);
        //需要删除的人员
        dbBoardUsers.removeAll(intersectionBoardUser);
        if (!dbBoardUsers.isEmpty()) {
            int deleteRow = boardUserDao.batchDeleteBoardUser(ea, arg.getBoardId(), dbBoardUsers);
            Preconditions.checkState(deleteRow > 0, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_738));
        }
        //需要添加的人员
        newBoardUsers.removeAll(intersectionBoardUser);
        if (!newBoardUsers.isEmpty()) {
            int insertRow = boardUserDao.batchInsertBoardUser(ea, arg.getBoardId(), newBoardUsers);
            Preconditions.checkState(insertRow > 0, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_744));
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<List<BoardCardActivityResult>> listBoardCardActivityByBoardCardId(String ea, ListBoardCardActivityArg arg) {
        Preconditions.checkArgument(ea != null && arg != null && !Strings.isNullOrEmpty(arg.getBoardCardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_751));
        return Result.newSuccess(boardManager.listBoardCardActivityByBoardCardId(ea, arg));
    }

    @Override
    public Result<Boolean> addCardComment(String ea, Integer fsUserId,String boardId, String boardCardId, String comment) {
        Preconditions.checkArgument(comment != null && !comment.isEmpty(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_757));
        //添加动态之评论
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        contentData.setComment(comment);
        return Result.newSuccess(boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), boardId, boardCardId, BoardCardActivityActionTypeEnum.COMMENT.getActionType(), contentData,
            false, BoardOperatorTypeEnum.USER.getOperator()));
    }

    @Override
    public Result<List<Integer>> listEmployeeIdByBoardId(String ea, String boardId) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(boardId), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_767));
        return Result.newSuccess(boardUserDao.listEmployeeIdByBoardId(ea, boardId));
    }

    @Override
    public Result<Boolean> updateBoardVisibleRangeById(String ea, Integer fsUserId, UpdateBoardVisibleRangeArg arg) {
        Preconditions.checkArgument(arg != null && !Strings.isNullOrEmpty(arg.getBoardId()) && !Strings.isNullOrEmpty(arg.getVisibleRange()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_580));
        Preconditions.checkArgument(BoardVisibleRangeEnum.isValid(arg.getVisibleRange()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_774));
        //查看当前用户是否在可见范围内，仅可见范围内可以更新看板
        Preconditions.checkState(checkFsUserAuthorization(ea, fsUserId, arg.getBoardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_776));
        BoardEntity dbBoardEntity = boardDao.getBoardByBoardId(arg.getBoardId(), ea);
        //根据数据库的可见范围和arg的可见范围，修改可见人员。如果设置为私有，则可见范围为本人。
        if (dbBoardEntity.getVisibleRange().equals(arg.getVisibleRange())) {
            return Result.newSuccess(true);
        }
        if (BoardVisibleRangeEnum.PUBLIC_VISIBLE_RANGE.getVisibleRange().equals(arg.getVisibleRange())) {
            int deleteRow = boardUserDao.batchDeleteBoardUserByBoardId(ea, arg.getBoardId());
            if (deleteRow == 0) {
                return Result.newSuccess(false);
            }
        } else if (BoardVisibleRangeEnum.PRIVATE_VISIBLE_RANGE.getVisibleRange().equals(arg.getVisibleRange())) {
            //可见范围为操作人本身
            ImmutableList<Integer> operator = ImmutableList.of(fsUserId);
            int insertRow = boardUserDao.batchInsertBoardUser(ea, arg.getBoardId(), operator);
            if (insertRow == 0) {
                return Result.newSuccess(false);
            }
        } else {
            return Result.newSuccess(false);
        }
        return Result.newSuccess(boardDao.updateBoardVisibleRangeById(arg.getBoardId(), arg.getVisibleRange()) > 0);
    }

    /**
     * 当创建看板时需要添加的动态
     */
    private boolean addBoardCardActivityWhenDeleteBoard(String ea, Integer fsUserId, String boardId) {
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        return boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), boardId, "CREATE", BoardCardActivityActionTypeEnum.DELETE_BOARD.getActionType(), contentData,
            false, BoardOperatorTypeEnum.USER.getOperator());
    }

    /**
     * 当更新卡片时需要添加的动态
     */
    private boolean addBoardCardActivityWhenUpdateBoardCard(String ea, Integer fsUserId, UpdateBoardCardArg updateBoardCardArg, BoardCardEntity dbBoardCard, BoardCardEntity newBoardCard) {
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        /* 添加看板动态之卡片名称 */
        if (!dbBoardCard.getName().equals(newBoardCard.getName())) {
            contentData.setBoardCardName(newBoardCard.getName());
            boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), updateBoardCardArg.getBoardId(), updateBoardCardArg.getBoardCardId(),
                BoardCardActivityActionTypeEnum.UPDATE_CARD_NAME.getActionType(), contentData, false, BoardOperatorTypeEnum.USER.getOperator());
            contentData.setBoardCardName(null);
        }
        /* 添加看板动态之卡片描述/备注 */
        //更新了备注情况
        if (dbBoardCard.getDescription() != null && !dbBoardCard.getDescription().equals(newBoardCard.getDescription())) {
            contentData.setBoardCardDescription(newBoardCard.getDescription());
            boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), updateBoardCardArg.getBoardId(), updateBoardCardArg.getBoardCardId(),
                BoardCardActivityActionTypeEnum.UPDATE_CARD_DESCRIPTION.getActionType(), contentData, false, BoardOperatorTypeEnum.USER.getOperator());
            contentData.setBoardCardDescription(null);
        }
        //新增了备注情况
        if (dbBoardCard.getDescription() == null && !Strings.isNullOrEmpty(newBoardCard.getDescription())) {
            contentData.setBoardCardDescription(newBoardCard.getDescription());
            boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), updateBoardCardArg.getBoardId(), updateBoardCardArg.getBoardCardId(),
                BoardCardActivityActionTypeEnum.UPDATE_CARD_DESCRIPTION.getActionType(), contentData, false, BoardOperatorTypeEnum.USER.getOperator());
            contentData.setBoardCardDescription(null);
        }
        /* 添加看板动态之关联市场活动 */
        if (!Strings.isNullOrEmpty(updateBoardCardArg.getMarketingEventId()) && !updateBoardCardArg.getMarketingEventId().equals(dbBoardCard.getMarketingEventId())) {
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, updateBoardCardArg.getMarketingEventId());
            if (marketingEventData != null && !Strings.isNullOrEmpty(marketingEventData.getName())) {
                contentData.setAssociateMarketingEventName(marketingEventData.getName());
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), updateBoardCardArg.getBoardId(), updateBoardCardArg.getBoardCardId(),
                    BoardCardActivityActionTypeEnum.ASSOCIATE_CARD_MARKETING_EVENT.getActionType(), contentData, false, BoardOperatorTypeEnum.USER.getOperator());
                contentData.setAssociateMarketingEventName(null);
            }
        }
        /* 添加看板动态之关联营销活动 */
        if (!Strings.isNullOrEmpty(updateBoardCardArg.getMarketingActivityId()) && !updateBoardCardArg.getMarketingActivityId().equals(dbBoardCard.getMarketingActivityId())) {
            GetMarketingActivityDetailVo marketingActivityDetailVo = marketingCrmManager.getByIdMarketingActivity(ea, fsUserId, updateBoardCardArg.getMarketingActivityId());
            if (marketingActivityDetailVo != null && !Strings.isNullOrEmpty(marketingActivityDetailVo.getName())) {
                contentData.setAssociateMarketingActivityName(marketingActivityDetailVo.getName());
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), updateBoardCardArg.getBoardId(), updateBoardCardArg.getBoardCardId(),
                    BoardCardActivityActionTypeEnum.ASSOCIATE_MARKETING_ACTIVITY.getActionType(), contentData, false, BoardOperatorTypeEnum.USER.getOperator());
                contentData.setAssociateMarketingActivityName(null);
            }
        }
        /* 添加看板动态之关联社会化分销 */
        if (!Strings.isNullOrEmpty(updateBoardCardArg.getDistributePlanId()) && !updateBoardCardArg.getDistributePlanId().equals(dbBoardCard.getDistributePlanId())) {
            DistributePlanEntity distributePlanById = distributionPlanDAO.getDistributePlanById(updateBoardCardArg.getDistributePlanId());
            if (distributePlanById != null && !Strings.isNullOrEmpty(distributePlanById.getPlanTitle())) {
                contentData.setDistributePlanName(distributePlanById.getPlanTitle());
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), updateBoardCardArg.getBoardId(), updateBoardCardArg.getBoardCardId(),
                    BoardCardActivityActionTypeEnum.ASSOCIATE_DISTRIBUTE_PLAN.getActionType(), contentData, false, BoardOperatorTypeEnum.USER.getOperator());
                contentData.setDistributePlanName(null);
            }
        }
        /* 设置卡片目标值 */
        if (!Strings.isNullOrEmpty(updateBoardCardArg.getGoalType()) && updateBoardCardArg.getGoalValue() != null && !updateBoardCardArg.getGoalValue().equals(dbBoardCard.getGoalValue())) {
            contentData.setBoardCardGoalValue(updateBoardCardArg.getGoalValue());
            contentData.setBoardCardGoalTypeName(BoardCardGoalType.getBoardCardLabelByType(updateBoardCardArg.getGoalType()));
            boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), updateBoardCardArg.getBoardId(), updateBoardCardArg.getBoardCardId(),
                BoardCardActivityActionTypeEnum.SET_CARD_GOAL.getActionType(), contentData, false, BoardOperatorTypeEnum.USER.getOperator());
            contentData.setBoardCardGoalValue(null);
            contentData.setBoardCardGoalTypeName(null);
        }
        /* 添加看板动态之工作项负责人 */
        List<Integer> newBoardCardPrincipals = CollectionUtils.isNotEmpty(newBoardCard.getPrincipals()) ? newBoardCard.getPrincipals() : new ArrayList<>();
        List<Integer> dbBoardCardPrincipals = dbBoardCard.getPrincipals() != null ? dbBoardCard.getPrincipals() : new ArrayList<>();
        //新增的工作项负责人
        newBoardCardPrincipals.removeAll(dbBoardCardPrincipals);
        if (!newBoardCardPrincipals.isEmpty()) {
            //依次添加工作项负责人的动态
            newBoardCardPrincipals.forEach(principal -> {
                contentData.setBoardCardPrincipal(principal);
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), updateBoardCardArg.getBoardId(), updateBoardCardArg.getBoardCardId(),
                    BoardCardActivityActionTypeEnum.UPDATE_CARD_PRINCIPAL.getActionType(), contentData, false, BoardOperatorTypeEnum.USER.getOperator());
            });
        }
        /* 工作项动态 */
        String updateStatus = updateBoardCardArg.getStatus();
        if (!Strings.isNullOrEmpty(updateStatus) && !updateStatus.equals(dbBoardCard.getStatus())) {
            contentData.setStatus(updateStatus);
            contentData.setBoardCardName(updateBoardCardArg.getName());
            String actionType;
            if (BoardCardStatus.FINISHED.getStatus().equals(updateStatus)) {
                actionType = BoardCardActivityActionTypeEnum.FINISH_WORK.getActionType();
            } else {
                actionType = BoardCardActivityActionTypeEnum.UNDO_WORK.getActionType();
            }
            boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), dbBoardCard.getBoardId(), dbBoardCard.getId(), actionType, contentData, false, BoardOperatorTypeEnum.USER.getOperator());
        }
        return true;
    }

    private boolean addBoardCardActivityWhenMoveBoardCard(String ea, Integer fsUserId,String boardId, String boardCardId, String sourceBoardCardListId, String targetBoardCardListId, String targetListName) {
        if (sourceBoardCardListId.equals(targetBoardCardListId)) {
            return true;
        }
        //添加看板动态之移动卡片
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();

        contentData.setMoveBoardCardFromBoardCardListName(boardCardListDao.getBoardCardListNameById(sourceBoardCardListId, ea));
        contentData.setMoveBoardCardToBoardCardListName(targetListName);
        boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), boardId, boardCardId, BoardCardActivityActionTypeEnum.MOVE_BOARD_CARD.getActionType(), contentData, false,
            BoardOperatorTypeEnum.USER.getOperator());
        return true;
    }

    private boolean addBoardCardActivityWhenDeleteBoardCardTask(String ea, Integer fsUserId, BoardCardTaskEntity boardCardTask) {
        //添加看板动态之删除任务
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();

        contentData.setBoardCardTaskName(boardCardTask.getName());
        boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), boardCardTask.getBoardId(), boardCardTask.getBoardCardId(), BoardCardActivityActionTypeEnum.DELETE_TASK.getActionType(),
            contentData, false, BoardOperatorTypeEnum.USER.getOperator());
        return true;
    }

    private boolean addBoardCardActivityWhenUpdateTaskStartTime(String ea, Integer fsUserId, UpdateBoardCardTaskArg arg, BoardCardTaskEntity dbBoardCardTaskEntity) {
        //如果时间相同，则不需要产生更新时间动态
        if (dbBoardCardTaskEntity.getStartTime() != null && arg.getStartTime().equals(dbBoardCardTaskEntity.getStartTime().getTime())) {
            return true;
        }
        //添加动态之更新任务的开始时间
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        contentData.setBoardCardTaskName(arg.getName());
        contentData.setUpdateBoardCardTaskStartTime(arg.getStartTime());
        boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), arg.getBoardId(), arg.getBoardCardId(), BoardCardActivityActionTypeEnum.UPDATE_TASK_START_TIME.getActionType(), contentData,
            false, BoardOperatorTypeEnum.USER.getOperator());
        return true;
    }

    private boolean addBoardCardActivityWhenUpdateTaskEndTime(String ea, Integer fsUserId, UpdateBoardCardTaskArg arg, BoardCardTaskEntity dbBoardCardTaskEntity) {
        //如果结束时间相同，则不需要产生更新时间动态
        if (dbBoardCardTaskEntity.getEndTime() != null && arg.getEndTime().equals(dbBoardCardTaskEntity.getEndTime().getTime())) {
            return true;
        }
        //添加动态之更新任务的开始时间
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        contentData.setUpdateBoardCardTaskEndTime(arg.getEndTime());
        contentData.setBoardCardTaskName(arg.getName());
        boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), arg.getBoardId(), arg.getBoardCardId(), BoardCardActivityActionTypeEnum.UPDATE_TASK_END_TIME.getActionType(), contentData,
            false, BoardOperatorTypeEnum.USER.getOperator());
        return true;
    }

    /**
     * 添加动态之认领任务或指派任务
     */
    private boolean addBoardCardActivityWhenUpdateBoardCardTaskExecutor(String ea, Integer fsUserId, UpdateBoardCardTaskArg arg, BoardCardTaskEntity dbBoardCardTask) {
        //更新的执行人为空，则任务没有执行人.如果执行人没有发生变化，则不产生动态
        if (arg.getExecutor() == null || arg.getExecutor().equals(dbBoardCardTask.getExecutor())) {
            return true;
        }
        //更新的执行人非空，数据库执行人为空，则为添加执行人
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        contentData.setBoardCardTaskName(arg.getName());
        if (dbBoardCardTask.getExecutor() == null) {
            //如果任务的处理人是自己，则为认领任务
            if (arg.getExecutor().equals(fsUserId)) {
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), arg.getBoardId(), arg.getBoardCardId(), BoardCardActivityActionTypeEnum.PULL_TASK.getActionType(), contentData,
                    false, BoardOperatorTypeEnum.USER.getOperator());
                return true;
            }
            //如果任务的处理人是他人，则为指派任务
            if (arg.getExecutor() != null) {
                contentData.setBoardCardTaskExecutor(arg.getExecutor());
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), arg.getBoardId(), arg.getBoardCardId(), BoardCardActivityActionTypeEnum.ASSIGN_TASK.getActionType(), contentData,
                    false, BoardOperatorTypeEnum.USER.getOperator());
                return true;
            }
        }
        //更新的执行人非空，数据库执行人非空，则根据执行人是否相同判断
        if (arg.getExecutor() != null && dbBoardCardTask.getExecutor() != null) {
            contentData.setBoardCardTaskExecutor(arg.getExecutor());
            //如果任务的处理人是自己，则为认领任务
            if (arg.getExecutor().equals(fsUserId)) {
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), arg.getBoardId(), arg.getBoardCardId(), BoardCardActivityActionTypeEnum.PULL_TASK.getActionType(), contentData,
                    false, BoardOperatorTypeEnum.USER.getOperator());
            }
            //如果任务的处理人是他人，则为指派任务
            else {
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), arg.getBoardId(), arg.getBoardCardId(), BoardCardActivityActionTypeEnum.ASSIGN_TASK.getActionType(), contentData,
                    false, BoardOperatorTypeEnum.USER.getOperator());
            }
        }
        return true;
    }

    private boolean addBoardCardActivityWhenUpdateBoardCardTaskStatus(String ea, Integer fsUserId, UpdateBoardCardTaskStatusArg arg, String dbStatus) {
        //更新成功下，并且任务状态发生了变化，则添加动态之任务动态
        if (!arg.getStatus().equals(dbStatus)) {
            String status = arg.getStatus();
            BoardCardActivityContentData contentData = new BoardCardActivityContentData();
            contentData.setBoardCardTaskName(arg.getTaskName());
            //完成任务的动态
            if (BoardCardTaskStatus.FINISHED.getStatus().equals(status)) {
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), arg.getBoardId(), arg.getBoardCardId(), BoardCardActivityActionTypeEnum.FINISH_TASK.getActionType(), contentData,
                    false, BoardOperatorTypeEnum.USER.getOperator());
            }
            //重做任务的动态
            else {
                boardManager.addBoardCardActivity(ea, String.valueOf(fsUserId), arg.getBoardId(), arg.getBoardCardId(), BoardCardActivityActionTypeEnum.REDO_TASK.getActionType(), contentData,
                    false, BoardOperatorTypeEnum.USER.getOperator());
            }
        }
        return true;
    }

    private void checkFsUserBoardCardTaskAuth(String boardCardTaskId){
        BoardCardTaskEntity boardCardTask = boardCardTaskDao.getById(boardCardTaskId);
        Preconditions.checkArgument(boardCardTask != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_433));
    }

    private void checkFsUserBoardCardAuth(String ea, String boardCardId){
        BoardCardEntity boardCard = boardCardDao.getById(boardCardId, ea);
        Preconditions.checkArgument(boardCard != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_248));
    }

    private void checkFsUserBoardCardListAuth(String ea, Integer fsUserId, String boardCardListId){
        BoardCardListEntity boardCardList = boardCardListDao.getById(boardCardListId, ea);
        Preconditions.checkArgument(boardCardList != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_1031));
        Preconditions.checkArgument(checkFsUserAuthorization(ea, fsUserId, boardCardList.getBoardId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_167));
    }

    /**
     * 查看用户是否在看板可见范围内
     */
    private boolean checkFsUserAuthorization(String ea, Integer fsUserId, String boardId) {
        Preconditions.checkArgument(fsUserId != null && boardId != null && StringUtils.isNotBlank(ea), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_751));
        BoardEntity dbBoardEntity = boardDao.getBoardByBoardId(boardId, ea);
        Preconditions.checkArgument(dbBoardEntity != null, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_836));
        //可见范围为public
        if (BoardVisibleRangeEnum.PUBLIC_VISIBLE_RANGE.getVisibleRange().equals(dbBoardEntity.getVisibleRange())) {
            return true;
        }
        String dbBoardId = boardUserDao.getBoardIdByEmployeeIdAndBoardId(ea, fsUserId, boardId);
        return StringUtils.isNotBlank(dbBoardId);
    }

    @Override
    public Result<Boolean> sendBoardCardTaskLifecycleChangeNotification(String boardCardTaskId) {
        BoardCardTaskEntity boardCardTask = boardCardTaskDao.getById(boardCardTaskId);
        if (boardCardTask == null){
            return Result.newSuccess(true);
        }

        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(boardCardTask.getEa()) || appVersionManager.getCurrentAppVersion(boardCardTask.getEa()) == null){
            log.info("BoardServiceImpl.sendBoardCardTaskLifecycleChangeNotification failed enterprise stop or license expire ea:{}", boardCardTask.getEa());
            return Result.newSuccess(false);
        }

        BoardEntity board = boardDao.getBoardByBoardId(boardCardTask.getBoardId(), boardCardTask.getEa());
        if (board == null){
            return Result.newSuccess(true);
        }
        BoardCardEntity boardCard = boardCardDao.getById(boardCardTask.getBoardCardId(), boardCardTask.getEa());
        if (boardCard == null){
            return Result.newSuccess(true);
        }
        if(BooleanUtils.isFalse(boardCardTask.getTaskEndNotifySent()) && boardCardTask.getEndTime().getTime() < System.currentTimeMillis() && boardCardTask.getExecutor() != null && !BoardCardTaskStatus.FINISHED.getStatus().equals(boardCardTask.getStatus())){
            boolean updated = boardCardTaskDao.changeTaskEndNotifySent(boardCardTaskId, false, true) != 0;
            if (updated){
                OfficeMessageArg.ContentWarp infoTitle = new OfficeMessageArg.ContentWarp();
                infoTitle.setContent("看板任务提醒");
                infoTitle.setInternationalContent("qx.ot.mark.kanban_task_reminder");
                List<OfficeMessageArg.LabelWarp> content = new LinkedList<>();
                content.add(OfficeMessageArg.LabelWarp.newInstance("描述", "qx.ot.mark.remark",
                        String.format("任务 “%s” 已到达截止时间，请及时处理", boardCardTask.getName()), "qx.ot.mark.task_deadline_reminder"));
                content.add(OfficeMessageArg.LabelWarp.newInstance("看板", "qx.ot.mark.kanban", board.getName()));
                content.add(OfficeMessageArg.LabelWarp.newInstance("工作项", "qx.ot.mark.task", boardCard.getName()));
                String fsUrl = host + "/ec/kemai/release/notice.html?pageName=kanban_detail&pageParams=${pageParams}&ea=" + board.getEa() + "#/notice/p";
                Map<String, String> pageParams = new HashMap<>(1);
                pageParams.put("id", boardCardTask.getBoardCardId());
                fsUrl = fsUrl.replace("${pageParams}", UrlUtils.urlEncode(GsonUtil.toJson(pageParams)));
                employeeMsgSender.sendUnionMessageAsync(boardCardTask.getEa(), -10000, ImmutableList.of(boardCardTask.getExecutor()), infoTitle, null, content, fsUrl, "cml://cmlMarketing/kanban_detail?id=${id}&id=${id}".replace("${id}", boardCardTask.getBoardCardId()), "/pkgs/pkg-kanban/pages/detail/detail?id=" + boardCardTask.getBoardCardId());
            }
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<Integer> detectGoalReachedBoardCardTaskAndSendNotification(String ea) {
        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
            log.info("BoardServiceImpl.startInstances detectGoalReachedBoardCardTaskAndSendNotification enterprise stop or license expire ea:{}", ea);
            return Result.newSuccess(null);
        }

        List<BoardCardEntity> boardCardEntities = boardCardDao.listAllNeedCheckGoalReachedTasks(ea);
        Map<String, BoardStatisticData> boardCardIdToStatisticDataMap = boardManager.getBoardCardStatisticDataMapByEntities(ea, boardCardEntities);
        int notifyCount = 0;
        for (BoardCardEntity boardCard : boardCardEntities) {
            BoardStatisticData statisticData = boardCardIdToStatisticDataMap.get(boardCard.getId());
            if (statisticData != null && statisticData.getGoalValueByGoalType(boardCard.getGoalType()) >= boardCard.getGoalValue()){
                boolean updated = boardCardDao.changeBoardCardGoalReachedNotifySentStatus(ea, boardCard.getId(), false, true) > 0;
                if (updated){
                    BoardEntity board = boardDao.getBoardByBoardId(boardCard.getBoardId(), ea);
                    if(board != null){
                        notifyCount++;
                        OfficeMessageArg.ContentWarp infoTitle = new OfficeMessageArg.ContentWarp();
                        infoTitle.setContent("看板动态通知");
                        infoTitle.setInternationalContent("qx.ot.mark.kanban_dynamic_notice");
                        List<OfficeMessageArg.LabelWarp> content = new LinkedList<>();
                        content.add(OfficeMessageArg.LabelWarp.newInstance("描述", "qx.ot.mark.remark",
                                String.format("看板工作项目标达成，%s 达到 %s", BoardCardGoalType.getBoardCardLabelByType(boardCard.getGoalType()), boardCard.getGoalValue()), "qx.ot.mark.kanban_task_finish"));
                        content.add(OfficeMessageArg.LabelWarp.newInstance("看板", "qx.ot.mark.kanban", board.getName()));
                        content.add(OfficeMessageArg.LabelWarp.newInstance("工作项", "qx.ot.mark.task", boardCard.getName()));
                        String fsUrl = host + "/ec/kemai/release/notice.html?pageName=kanban_detail&pageParams=${pageParams}&ea=" + ea + "#/notice/p";
                        Map<String, String> pageParams = new HashMap<>(1);
                        pageParams.put("id", boardCard.getId());
                        fsUrl = fsUrl.replace("${pageParams}", UrlUtils.urlEncode(GsonUtil.toJson(pageParams)));
                        employeeMsgSender.sendUnionMessageAsync(ea, -10000, boardCard.getPrincipals(), infoTitle, null, content, fsUrl,"cml://cmlMarketing/kanban_detail?id=${id}&id=${id}".replace("${id}", boardCard.getId()), "/pkgs/pkg-kanban/pages/detail/detail?id=" + boardCard.getId());
                    }
                }
            }
        }
        return Result.newSuccess(notifyCount);
    }

    @Override
    public Result<EnterpriseBoardTemplateVo> addEnterpriseTemplate(String ea, Integer fsUserId, AddEnterpriseTemplateArg arg) {
        if (!AddEnterpriseTemplateArg.isArgValid(arg)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String aPath = null;
        if (arg.getCover().contains(".")){
            aPath = fileV2Manager.changeTempFileToPermanent(ea, fsUserId, ".jpg", arg.getCover().substring(0, arg.getCover().indexOf(".")));
        }else {
            aPath = fileV2Manager.changeTempFileToPermanent(ea, fsUserId, ".jpg", arg.getCover());
        }
        if (StringUtils.isBlank(aPath)) {
            return Result.newError(SHErrorCode.FILE_UPLOAD_FAILED);
        }
        String url = fileV2Manager.getUrlByPath(aPath, ea, false);
        if (StringUtils.isBlank(url)) {
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }
        String id = UUIDUtil.getUUID();
        BoardEntity boardEntity = new BoardEntity();
        boardEntity.setId(id);
        boardEntity.setEa(ea);
        boardEntity.setCreator(fsUserId);
        boardEntity.setVisibleRange(BoardVisibleRangeEnum.PRIVATE_VISIBLE_RANGE.getVisibleRange());
        boardEntity.setType(BoardTypeEnum.DATA_TYPE.getType());
        boardEntity.setTemplateType(BoardTemplateTypeEnum.ENTERPRISE_TYPE.getTemplateType());
        boardEntity.setName(arg.getName());
        boardEntity.setCover(url);
        boardEntity.setSceneType(arg.getSceneType());
        if (boardDao.insertBoard(boardEntity) != 1) {
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }
        boardUserDao.batchInsertBoardUser(ea, id, ImmutableList.of(fsUserId));
        EnterpriseBoardTemplateVo result = new EnterpriseBoardTemplateVo();
        BeanUtils.copyProperties(boardEntity, result);
        FsAddressBookManager.FSEmployeeMsg employeeInfo = fsAddressBookManager.getEmployeeInfo(ea, fsUserId);
        String creatorName = employeeInfo != null ? (StringUtils.isNotBlank(employeeInfo.getName()) ? employeeInfo.getName() : employeeInfo.getFullName()) : "";
        result.setCreatorName(creatorName);
        return Result.newSuccess(result);
    }

    @Override
    public Result<Integer> updateBoardMenu(String ea, Integer fsUserId, UpdateBoardMenuArg arg) {
        String boardId = arg.getBoardId();
        Preconditions.checkArgument(!Strings.isNullOrEmpty(boardId), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_240));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getAssociatedObjectType()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_1163));
        BoardEntity dbBoardEntity = boardDao.getBoardByBoardId(boardId, ea);
        Preconditions.checkArgument(dbBoardEntity != null, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_836));

        BoardEntity boardToUpdateMenu = new BoardEntity();
        BeanUtils.copyProperties(arg, boardToUpdateMenu, "boardId");
        boardToUpdateMenu.setId(boardId);
        int updateRow = boardDao.updateBoardMenu(boardToUpdateMenu);
        if (updateRow > 0) {
            addBoardMenuActivityWhenUpdateBoardMenu(ea, fsUserId, arg, dbBoardEntity);
            // 更新目标人群
            boardToMarketingUserGroupRelationDao.deleteMarketingUserGroupByBoardId(ea, boardId);
            if (arg.getMarketingUserGroupIds() != null){
                for (String marketingUserGroupId : arg.getMarketingUserGroupIds()) {
                    boardToMarketingUserGroupRelationDao.insertIgnore(ea, boardId, marketingUserGroupId);
                }
            }
        }

        // 重置
        BoardResult boardResult = new BoardResult();
        BoardEntity boardEntity = boardDao.getNeedResetCheckGoalReachedTaskById(ea, boardId);
        if (boardEntity != null) {
            BeanUtils.copyProperties(boardEntity, boardResult);
            BoardStatisticData statisticData = boardManager.doGetBoardMainTargetStatisticData(ea, boardResult).get(boardId);
            if (statisticData == null || statisticData.getGoalValueByGoalType(boardEntity.getGoalType()) < boardEntity.getGoalValue()){
                boardDao.changeBoardGoalReachedNotifySentStatus(ea, boardId, true, false);
            }
        }
        if (updateRow == 1) {
            if (BoardCardGoalType.WORK_FINISHED_COUNT.getType().equals(arg.getGoalType())) {
                return Result.newSuccess(boardResult.getGoalValue() == null ? boardCardDao.queryCardCountById(ea, boardId) : boardResult.getGoalValue());
            }
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
    }

    @Override
    public Result<BoardMenuResult> getBoardMenuDetail(String ea, Integer fsUserId, String boardId) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(boardId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        BoardMenuEntity boardMenuEntity = boardDao.queryMenuById(ea, boardId);
        if (boardMenuEntity == null) {
            if (!Strings.isNullOrEmpty(boardDao.getBoardNameByBoardId(boardId, "__SYSTEM"))) {
                return Result.newSuccess();
            }
            return Result.newError(SHErrorCode.BOARD_NOT_EXIST);
        }
        if (boardMenuEntity.getTemplateType() == 1) {
            return Result.newSuccess();
        }
        BoardMenuResult boardMenuResult = boardMenuEntity.toResult();
        // 添加 目标对象名称targetObjectName
        BoardResult boardResult = new BoardResult();
        BeanUtils.copyProperties(boardMenuResult, boardResult);
        boardManager.fillTargetObjectNameToBoardResult(ea, boardResult);
        boardMenuResult.setTargetObjectName(boardResult.getTargetObjectName());
        // 添加 目标完成情况targetResult
        boardManager.fillStatisticDataToBoardTargetResult(ea, boardResult);
        boardMenuResult.setStatisticData(boardResult.getStatisticData());
        if (BoardCardGoalType.WORK_FINISHED_COUNT.getType().equals(boardMenuEntity.getGoalType())) {
            boardMenuResult.setGoalValue(boardResult.getGoalValue());
        }
        // 添加 营销人群ID列表marketingUserGroupIds
        boardMenuResult.setMarketingUserGroupIds(boardToMarketingUserGroupRelationDao.queryMarketingUserGroupIds(ea, boardId));
        return Result.newSuccess(boardMenuResult);
    }

    @Override
    public Result<List<BoardCardActivityResult>> listBoardActivityByBoardId(String ea, ListBoardActivityArg arg) {
        String boardId = arg.getBoardId();
        if (StringUtils.isEmpty(boardId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getPageNo() == null || arg.getPageSize() == null) {
            arg.setPageNo(1);
            arg.setPageSize(10);
        }
        List<BoardCardActivityEntity> activityEntities = boardCardActivityDao.listBoardCardActivityByBoardId(ea, boardId, arg.getLimit(), arg.getOffset());
        if (CollectionUtils.isEmpty(activityEntities)) {
            return Result.newSuccess(new LinkedList<>());
        }
        //所有的操作者id，用于创建缓存
        List<Integer> operators = activityEntities.stream().filter(boardCardActivityEntity -> BoardOperatorTypeEnum.USER.getOperator().equals(boardCardActivityEntity.getOperatorType()))
            .map(BoardCardActivityEntity::getOperator).filter(Objects::nonNull).map(Integer::valueOf).distinct().collect(Collectors.toList());
        Map<Integer, FSEmployeeMsg> fsUserIdToNameCache = fsAddressBookManager.getEmployeeInfoByUserIds(ea, operators, true);
        return Result.newSuccess(activityEntities.stream().map(activityEntity -> {
            BoardCardActivityResult boardCardActivityResult = new BoardCardActivityResult();
            boardManager.boardCardActivityEntityToBoardCardActivityResult(ea, activityEntity, boardCardActivityResult, fsUserIdToNameCache);
            return boardCardActivityResult;
        }).collect(Collectors.toList()));
    }

    @Override
    public Result<BoardListCheckResult> checkBoardAndListAccessStatus(String ea, Integer fsUserId, String boardId, String boardCardListId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(boardId), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_1260));
        BoardEntity boardEntity = boardDao.getByIdWhateverLiveStatus(boardId, ea);
        Preconditions.checkArgument(boardEntity != null, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_836));

        BoardListCheckResult result = new BoardListCheckResult();
        result.setBoardId(boardId);
        result.setBoardCardListId(boardCardListId);
        Integer boardLifeStatus = boardEntity.getLifeStatus();
        if (1 == boardLifeStatus) {
            result.setBoardAccessStatus("delete");
            result.setBoardCardListAccessStatus("delete");
        } else {
            result.setBoardName(boardEntity.getName());
            String visibleRange = boardEntity.getVisibleRange();
            if (fsUserId.equals(boardEntity.getCreator()) || BoardVisibleRangeEnum.PUBLIC_VISIBLE_RANGE.getVisibleRange().equals(visibleRange) ||
                    boardId.equals(boardUserDao.getBoardIdByEmployeeIdAndBoardId(ea, fsUserId, boardId))) {
                result.setBoardAccessStatus("visible");
            } else {
                    result.setBoardAccessStatus("invisible");
            }
            if (StringUtils.isNotEmpty(boardCardListId)) {
                BoardCardListEntity boardCardListEntity = boardCardListDao.getByIdWhateverLiveStatus(boardCardListId, ea);
                if (boardCardListEntity == null || !boardId.equals(boardCardListEntity.getBoardId())) {
                    result.setBoardCardListAccessStatus("nonexistent");
                } else {
                    if (1 == boardCardListEntity.getLifeStatus()) {
                        result.setBoardCardListAccessStatus("delete");
                    } else {
                        result.setBoardCardListName(boardCardListEntity.getName());
                        result.setBoardCardListAccessStatus("normal");
                    }
                }
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<EnterpriseBoardTemplateVo>> listBoardTemplateBySceneType(String ea, Integer fsUserId, String sceneType) {
        Preconditions.checkArgument(MarketingSceneType.isValid(sceneType), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_1299));
        List<BoardEntity> boardEntities = new ArrayList<>();
        boardEntities.add(boardDao.getBoardByBoardId("KBMB", "__SYSTEM"));
        boardEntities.addAll(boardDao.listEnterpriseTemplateBoardBySceneType(ea, sceneType));
        boardEntities.addAll(boardDao.listEnterpriseTemplateBoardBySceneType("__SYSTEM", sceneType));
        List<EnterpriseBoardTemplateVo> result = boardEntities.stream().map(boardEntity -> {
            EnterpriseBoardTemplateVo vo = new EnterpriseBoardTemplateVo();
            BeanUtils.copyProperties(boardEntity, vo);
            return vo;
        }).collect(Collectors.toList());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Boolean> updateTemplateInfo(String ea, Integer fsUserId, UpdateBoardTemplateArg arg) {
        Preconditions.checkArgument(arg != null && arg.isArgValid(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_1314));
        BoardEntity board = boardDao.getBoardByBoardId(arg.getId(), ea);
        if (board == null || board.getTemplateType() != 1) {
            return Result.newError(SHErrorCode.FORM_TEMPLATE_NOT_EXIST);
        }
        // 仅创建者允许编辑
        if (!fsUserId.equals(board.getCreator())) {
            return Result.newError(SHErrorCode.USER_DOSE_NOT_HAVE_PERMISSION);
        }
        if (!arg.getCover().equals(board.getCover())) {
            String aPath = fileV2Manager.changeTempFileToPermanent(ea, fsUserId, ".jpg", arg.getCover().substring(0, arg.getCover().indexOf(".")));
            if (StringUtils.isBlank(aPath)) {
                return Result.newError(SHErrorCode.FILE_UPLOAD_FAILED);
            }
            String url = fileV2Manager.getUrlByPath(aPath, ea, false);
            if (StringUtils.isBlank(url)) {
                return Result.newError(SHErrorCode.FILE_NOT_FOUND);
            }
            arg.setCover(url);
        }
        int row = boardDao.updateTemplateInfo(arg.getId(), arg.getName(), arg.getSceneType(), arg.getCover());
        return Result.newSuccess(row > 0);
    }

    @Override
    public Result<SOPBoardTabResult> getSOPTabInfo(String ea, Integer fsUserId, GetSopInfoArg arg) {
        Preconditions.checkArgument(arg != null && StringUtils.isNotEmpty(arg.getObjectId()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_1340));
        String boardId = boardDao.getSopBoardIdByObjectId(ea, arg.getObjectId());
        SOPBoardTabResult result = new SOPBoardTabResult();
        if (StringUtils.isEmpty(boardId)) {
            return Result.newSuccess(result);
        }
        result.setExist(true);
        result.setSopBoardId(boardId);
        result.setWorkCount(boardCardDao.queryCardCountById(ea, boardId));
        result.setFinishedWorkCount(boardCardDao.queryFinishedCardCountById(ea, boardId));
        return Result.newSuccess(result);
    }

    /**
     * 当更新菜单时需要添加的动态
     */
    private boolean addBoardMenuActivityWhenUpdateBoardMenu(String ea, Integer fsUserId, UpdateBoardMenuArg arg, BoardEntity dbBoardEntity) {
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
        /* 添加看板动态之关联市场活动 */
        if (!Strings.isNullOrEmpty(arg.getMarketingEventId()) && !arg.getMarketingEventId().equals(dbBoardEntity.getMarketingEventId())) {
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, arg.getMarketingEventId());
            if (marketingEventData != null && !Strings.isNullOrEmpty(marketingEventData.getName())) {
                contentData.setAssociateMarketingEventName(marketingEventData.getName());
                boardManager.addBoardMenuActivity(ea, fsUserId, arg.getBoardId(), BoardCardActivityActionTypeEnum.ASSOCIATE_CARD_MARKETING_EVENT.getActionType(), contentData, false);
                contentData.setAssociateMarketingEventName(null);
            }
        }
        /* 添加看板动态之关联营销活动 */
        if (!Strings.isNullOrEmpty(arg.getMarketingActivityId()) && !arg.getMarketingActivityId().equals(dbBoardEntity.getMarketingActivityId())) {
            GetMarketingActivityDetailVo marketingActivityDetailVo = marketingCrmManager.getByIdMarketingActivity(ea, fsUserId, arg.getMarketingActivityId());
            if (marketingActivityDetailVo != null && !Strings.isNullOrEmpty(marketingActivityDetailVo.getName())) {
                contentData.setAssociateMarketingActivityName(marketingActivityDetailVo.getName());
                boardManager.addBoardMenuActivity(ea, fsUserId, arg.getBoardId(), BoardCardActivityActionTypeEnum.ASSOCIATE_MARKETING_ACTIVITY.getActionType(), contentData, false);
                contentData.setAssociateMarketingActivityName(null);
            }
        }
        /* 添加看板动态之关联社会化分销 */
        if (!Strings.isNullOrEmpty(arg.getDistributePlanId()) && !arg.getDistributePlanId().equals(dbBoardEntity.getDistributePlanId())) {
            DistributePlanEntity distributePlanById = distributionPlanDAO.getDistributePlanById(arg.getDistributePlanId());
            if (distributePlanById != null && !Strings.isNullOrEmpty(distributePlanById.getPlanTitle())) {
                contentData.setDistributePlanName(distributePlanById.getPlanTitle());
                boardManager.addBoardMenuActivity(ea, fsUserId, arg.getBoardId(), BoardCardActivityActionTypeEnum.ASSOCIATE_DISTRIBUTE_PLAN.getActionType(), contentData, false);
                contentData.setDistributePlanName(null);
            }
        }
        /* 设置卡片目标值 */
        if (!Strings.isNullOrEmpty(arg.getGoalType()) && arg.getGoalValue() != null && !arg.getGoalValue().equals(dbBoardEntity.getGoalValue())) {
            contentData.setBoardCardGoalValue(arg.getGoalValue());
            contentData.setBoardCardGoalTypeName(BoardCardGoalType.getBoardCardLabelByType(arg.getGoalType()));
            boardManager.addBoardMenuActivity(ea, fsUserId, arg.getBoardId(), BoardCardActivityActionTypeEnum.SET_CARD_GOAL.getActionType(), contentData, false);
            contentData.setBoardCardGoalValue(null);
            contentData.setBoardCardGoalTypeName(null);
        }
        return true;
    }

    @Override
    public Result<List<EnterpriseBoardTemplateVo>> listEnterpriseBoardTemplate(String ea, Integer fsUserId) {
        List<BoardEntity> boardEntities = boardDao.listEnterpriseTemplateBoard(ea);
        if (CollectionUtils.isEmpty(boardEntities)) {
            return Result.newSuccess(new LinkedList<>());
        }
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> creatorToFsEmployeeMsg = fsAddressBookManager.getEmployeeInfoByUserIds(ea, boardEntities.stream().map(BoardEntity::getCreator).collect(Collectors.toList()), false);
        List<BoardUserEntity> boardUserEntities = boardUserDao.listBoardUserEntitiesByEaAndBoardIds(ea, boardEntities.stream().map(BoardEntity::getId).collect(Collectors.toList()), fsUserId);
        // 与fsUserId有关企业模板
        Set<String> fsUserIdRelateWithBoardIds = boardUserEntities.stream().map(BoardUserEntity::getBoardId).collect(Collectors.toSet());
        List<EnterpriseBoardTemplateVo> result = boardEntities.stream().map(boardEntity -> {
            EnterpriseBoardTemplateVo vo = new EnterpriseBoardTemplateVo();
            BeanUtils.copyProperties(boardEntity, vo);
            FSEmployeeMsg fsEmployeeMsg = creatorToFsEmployeeMsg.get(boardEntity.getCreator());
            if (fsEmployeeMsg == null) {
                vo.setCreatorName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_1411));
            } else {
                vo.setCreatorName(StringUtils.isBlank(fsEmployeeMsg.getFullName()) ? fsEmployeeMsg.getName() : fsEmployeeMsg.getFullName());
            }
            Boolean haveAuthority = BoardVisibleRangeEnum.PUBLIC_VISIBLE_RANGE.getVisibleRange().equals(boardEntity.getVisibleRange()) || fsUserIdRelateWithBoardIds.contains(boardEntity.getId());
            vo.setHaveAuthority(haveAuthority);
            return vo;
        }).collect(Collectors.toList());
        return Result.newSuccess(result);
    }
}