package com.facishare.marketing.provider.remote.restapi;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.UserTagConstants;
import com.facishare.marketing.common.enums.I18nKeyStaticEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.MarketingUserGroupCustomizeObjectMappingDao;
import com.facishare.marketing.provider.dao.UserMarketingCrmWxWorkExternalUserRelationDao;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.entity.MarketingUserGroupCustomizeObjectMappingEntity;
import com.facishare.marketing.provider.innerArg.MetadataTagFindByTemplateArg;
import com.facishare.marketing.provider.innerArg.qywx.AddCorpTagArg;
import com.facishare.marketing.provider.innerArg.qywx.GetCorpTagListArg;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.innerResult.qywx.AddCorpTagResult;
import com.facishare.marketing.provider.innerResult.qywx.GetCorpTagListResult;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.metadata.PublicPurgeService;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.model.result.Tag;
import com.facishare.wechat.proxy.model.result.TagUsersResult;
import com.facishare.wechat.proxy.outer.model.arg.TaggingArg;
import com.facishare.wechat.proxy.service.TagService;
import com.facishare.wechat.proxy.service.WechatUserTagService;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.ConnectorEnum;
import com.fxiaoke.crmrestapi.common.contants.TagOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.WechatFanFieldContants;
import com.fxiaoke.crmrestapi.common.contants.WechatWorkExternalUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.MetadataTagResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.MetadataTagDataService;
import com.fxiaoke.crmrestapi.service.MetadataTagService;
import com.fxiaoke.crmrestapi.service.TagRestService;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.*;
import com.mysql.jdbc.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 27/03/2019
 */
@Slf4j
@Component
public class MetadataTagManager {
    private static final String MARKETING_TAG_GROUP_NAME = I18nKeyStaticEnum.MARK_STATIC_MARKETINGTAGMANAGER_MARKETING_TAG_GROUP_NAME.getKey();
    @Autowired
    private MetadataTagService metadataTagService;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private TagService wxFanTagService;
    @Autowired
    private WechatUserTagService wechatUserTagService;
    @Autowired
    private MarketingUserGroupCustomizeObjectMappingDao customizeObjectMappingDao;

    @Autowired
    private UserMarketingCrmWxWorkExternalUserRelationDao userMarketingCrmWxWorkExternalUserRelationDao;

    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private TagRestService tagRestService;

    private final LoadingCache<String, String> cache = CacheBuilder.newBuilder().maximumSize(40960).build(new CacheLoader<String, String>() {
        @Override
        public String load(String key) throws Exception {
            List<String> keys = Splitter.on('`').splitToList(key);
            String ea = keys.get(0);
            String describeApiName = keys.get(1);
            String describeApiNameLabel = null;
            CrmObjectApiNameEnum apiNameEnum = CrmObjectApiNameEnum.fromName(describeApiName);
            if (apiNameEnum != null){
                describeApiNameLabel = apiNameEnum.getLabel();
            }else {
                List<MarketingUserGroupCustomizeObjectMappingEntity> mappingEntities = customizeObjectMappingDao.getByEa(ea);
                Map<String, MarketingUserGroupCustomizeObjectMappingEntity> customizeObjectMappingEntityMap = mappingEntities.stream().collect(Collectors.toMap(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName, Function.identity(), (v1, v2)->v1));
                describeApiNameLabel = customizeObjectMappingEntityMap.get(describeApiName).getObjectName();
            }

            Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
            TagGroupGetGroupTagByNameArg firstNameArg = new TagGroupGetGroupTagByNameArg(tenantId, describeApiName, I18nUtil.getStaticByKey(MARKETING_TAG_GROUP_NAME) + "-" + describeApiNameLabel);
            MetadataTagResult<MetadataTagGroupData> firstGetTagGroupResult = metadataTagService.getGroupTagByName(firstNameArg);
            Preconditions.checkState(firstGetTagGroupResult.isSuccess());
            if (firstGetTagGroupResult.getResult() != null) {
                return firstGetTagGroupResult.getResult().getId();
            }
            // 如果存在老的标签组名，则将其改名
            TagGroupGetGroupTagByNameArg oldGroupNameArg = new TagGroupGetGroupTagByNameArg(tenantId, describeApiName, I18nUtil.getStaticByKey(MARKETING_TAG_GROUP_NAME));
            MetadataTagResult<MetadataTagGroupData> getOldTagGroupResult = metadataTagService.getGroupTagByName(oldGroupNameArg);
            Preconditions.checkState(getOldTagGroupResult.isSuccess());
            if (getOldTagGroupResult.getResult() != null) {
                TagGroupUpdateArg tagGroupUpdateArg = new TagGroupUpdateArg(tenantId, getOldTagGroupResult.getResult().getId(), describeApiName, I18nUtil.getStaticByKey(MARKETING_TAG_GROUP_NAME) + "-" + describeApiNameLabel);
                MetadataTagResult<MetadataTagGroupData> updateGroupResult = metadataTagService.updateGroup(tagGroupUpdateArg);
                Preconditions.checkState(updateGroupResult.isSuccess());
                return getOldTagGroupResult.getResult().getId();
            }
            TagGroupGetGroupTagByNameArg secondNameArg = new TagGroupGetGroupTagByNameArg(tenantId, describeApiName, I18nUtil.getStaticByKey(MARKETING_TAG_GROUP_NAME) + "-" + describeApiNameLabel);
            MetadataTagResult<MetadataTagGroupData> secondGetTagGroupResult = metadataTagService.getGroupTagByName(secondNameArg);
            Preconditions.checkState(secondGetTagGroupResult.isSuccess());
            if (secondGetTagGroupResult.getResult() != null) {
                return secondGetTagGroupResult.getResult().getId();
            }
            TagGroupCreateArg createTagGroupArg = new TagGroupCreateArg(tenantId, describeApiName, I18nUtil.getStaticByKey(MARKETING_TAG_GROUP_NAME) + "-" + describeApiNameLabel);
            MetadataTagResult<MetadataTagGroupData> createTagGroupResult = metadataTagService.createGroup(createTagGroupArg);
            Preconditions.checkState(createTagGroupResult.isSuccess());
            return createTagGroupResult.getResult().getId();
        }
    });
    @Autowired
    private MetadataTagDataService metadataTagDataService;
    @Autowired
    private PublicPurgeService publicPurgeService;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private CrmV2Manager crmV2Manager;

    /**
     * 根据标签名称集合返回标签名称到标签Id的映射, 任何一个合法的TagName都可以得到tagId，不存在tagId为null的情况。
     */
    public Map<TagName, String> getOrCreateTagIdsByTagNames(String ea, String describeApiName, Collection<TagName> tagNames) {
        Preconditions.checkArgument(tagNames != null && !tagNames.isEmpty());
        Map<TagName, String> tagNameToIdMap = new HashMap<>(tagNames.size());
        for (TagName tagName : tagNames) {
            Preconditions.checkArgument(tagName != null);
            tagNameToIdMap.put(tagName, getOrCreateTagIdByTagName(ea, describeApiName, tagName));
        }
        return tagNameToIdMap;
    }

    /**
     * 根据标签名称返回标签Id, 任何一个合法的TagName都可以得到tagId，不存在tagId为null的情况。
     */
    public String getOrCreateTagIdByTagName(String ea, String describeApiName, TagName tagName) {
        Preconditions.checkArgument(tagName != null);
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        String tagGroupId = doGetOrCreateMarketingTagGroup(ea, describeApiName);
        String newFirstTagId = doGetTag(tenantId, tagGroupId, null, tagName.getCombineName());
        if(newFirstTagId != null){
            return newFirstTagId;
        }
        String firstTagId = doGetTag(tenantId, tagGroupId, null, tagName.getFirstTagName());
        if(firstTagId != null && !Strings.isNullOrEmpty(tagName.getSecondTagName())){
            String secondTagId = doGetTag(tenantId, tagGroupId, firstTagId,  tagName.getSecondTagName());
            if(secondTagId != null){
                migrateToFirstLevelTag(tenantId, secondTagId, tagName.getCombineName());
                return secondTagId;
            }
        }
        return doGetOrCreateTag(tenantId, describeApiName, tagGroupId, null, 1, tagName.getCombineName());
    }

    // 只用于数据迁移
    public String tryMigrateTagByTagName(String ea, String describeApiName, TagName tagName) {
        Preconditions.checkArgument(tagName != null);
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        String tagGroupId = doGetOrCreateMarketingTagGroup(ea, describeApiName);
        String newFirstTagId = doGetTag(tenantId, tagGroupId, null, tagName.getCombineName());
        if(newFirstTagId != null){
            return newFirstTagId;
        }
        String firstTagId = doGetTag(tenantId, tagGroupId, null, tagName.getFirstTagName());
        if(firstTagId != null && !Strings.isNullOrEmpty(tagName.getSecondTagName())){
            String secondTagId = doGetTag(tenantId, tagGroupId, firstTagId,  tagName.getSecondTagName());
            if(secondTagId != null){
                migrateToFirstLevelTag(tenantId, secondTagId, tagName.getCombineName());
                return secondTagId;
            }
        }
        return null;
    }

    private void migrateToFirstLevelTag(Integer tenantId, String tagId, String newName){
        MetadataTagData metadataTagData = new MetadataTagData();
        metadataTagData.setId(tagId);
        metadataTagData.setTenantId(tenantId + "");
        metadataTagData.setName(newName);
        metadataTagData.setGrade(UserTagConstants.FIRST_GRADE);
        metadataTagData.setSupTag(null);
        TagUpdateByKeysArg tagUpdateByKeysArg = new TagUpdateByKeysArg(tenantId, -10000, metadataTagData, ImmutableList.of("name", "grade", "sup_tag"));
        MetadataTagResult<MetadataTagData> metadataTagResult = metadataTagService.updateByKeys(tagUpdateByKeysArg);
        Preconditions.checkArgument(metadataTagResult.isSuccess());
    }

    public Set<String> listTagNamesBySupTagId(String ea, String describeApiName, String supTagId){
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(describeApiName));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(supTagId));
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        TagGroupFindTagsByGroupIdArg arg = new TagGroupFindTagsByGroupIdArg(tenantId, describeApiName, doGetOrCreateMarketingTagGroup(ea, describeApiName), null, 1, 10000);
        MetadataTagResult<TagPage<MetadataTagData>> result = metadataTagService.findTagsByGroupId(arg);
        if(!result.isSuccess() || result.getResult().getData() == null){
            return new HashSet<>(0);
        }
        return result.getResult().getData().stream().filter(tag -> tag.getGrade() != null && tag.getGrade() == 2 && supTagId.equals(tag.getSupTag())).map(MetadataTagData::getName).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    /**
     * 根据标签名称列表拉取标签名称到id的映射
     */
    public Map<TagName, String> getTagIdsByTagNames(String ea, String describeApiName, Collection<TagName> tagNames) {
        Preconditions.checkArgument(tagNames != null && !tagNames.isEmpty());
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        Map<TagName, String> tagNameToIdMap = new HashMap<>(tagNames.size());
        for (TagName tagName : tagNames) {
            Preconditions.checkArgument(tagName != null);
            String group = doGetOrCreateMarketingTagGroup(ea, describeApiName);
            String tagId = doGetTag(tenantId, group, "", tagName.getCombineName());
            if(tagId != null){
                tagNameToIdMap.put(tagName, tagId);
            }
        }
        return tagNameToIdMap;
    }

    public Map<String, TagName> getTagNamesByTagIds(String ea, String describeApiName, Collection<String> tagIds) {
        if (tagIds == null || tagIds.isEmpty()) {
            return new HashMap<>(0);
        }
        String marketingTagGroupId = doGetOrCreateMarketingTagGroup(ea, describeApiName);
        ListTagByIdsArg listSecondGradeTagByIdsArg = new ListTagByIdsArg();
        listSecondGradeTagByIdsArg.setTenantId(eieaConverter.enterpriseAccountToId(ea) + "");
        listSecondGradeTagByIdsArg.setSubTagIdList(new ArrayList<>(tagIds));
        MetadataTagResult<List<MetadataTagData>> metadataTagResult = metadataTagService.listTagByIds(listSecondGradeTagByIdsArg);
        Preconditions.checkState(metadataTagResult.isSuccess());
        return metadataTagResult.getResult().stream().filter(tag -> marketingTagGroupId.equals(tag.getTagId())).filter(tag -> tag.getGrade() == 1).collect(Collectors.toMap(MetadataTagData::getId, metadataTagData -> {
                    List<String> sp = Splitter.on(':').splitToList(metadataTagData.getName());
                    if (sp.size() == 2){
                        return new TagName(sp.get(0), sp.get(1));
                    }
                    return new TagName(sp.get(0), null);
                }, (v1, v2) -> v1));
    }

    public String doGetOrCreateMarketingTagGroup(String ea, String describeApiName) {
        try {
            return cache.get(ea + '`' + describeApiName);
        } catch (Exception e) {
            throw new RuntimeException(e.getCause());
        }
    }

    private String doGetTag(Integer tenantId, String tagGroupId, String supTagId, String tagName){
        TagFindTagByTagNameArg tagFindArg = new TagFindTagByTagNameArg(tenantId, tagGroupId, supTagId, tagName);
        MetadataTagResult<MetadataTagData> tagFindResult = metadataTagService.findTagByTagName(tagFindArg);
        Preconditions.checkState(tagFindResult.isSuccess());
        return tagFindResult.getResult() == null ? null : tagFindResult.getResult().getId();
    }

    private String doGetOrCreateTag(Integer tenantId, String describeApiName, String tagGroupId, String supTagId, Integer grade, String tagName) {
        TagFindTagByTagNameArg tagFindArg = new TagFindTagByTagNameArg(tenantId, tagGroupId, supTagId, tagName);
        MetadataTagResult<MetadataTagData> tagFindResult = metadataTagService.findTagByTagName(tagFindArg);
        Preconditions.checkState(tagFindResult.isSuccess());
        if (tagFindResult.getResult() == null) {
            TagCreateTagArg tagCreateArg = new TagCreateTagArg(tenantId, -10000, describeApiName, tagGroupId, supTagId, grade, tagName);
            MetadataTagResult<MetadataTagData> tagCreateResult = metadataTagService.createTag(tagCreateArg);
            Preconditions.checkState(tagCreateResult.isSuccess());
            return tagCreateResult.getResult().getId();
        }
        return tagFindResult.getResult().getId();
    }

    //这个方法用于给数据打标签，该方法不会删除数据上原有的标签,以追加的形式打标签。
    public void addTagsToObjectDatas(String ea, List<ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListData) {
        Map<String, Map<TagNameList, List<String>>> apiNameAndTagNameListAndObjectIdsMap = objectDataIdAndTagNameListData.stream().filter(ObjectDataIdAndTagNameListData::valid).collect(Collectors
            .groupingBy(ObjectDataIdAndTagNameListData::getApiName,
                Collectors.groupingBy(ObjectDataIdAndTagNameListData::getTagNameList, Collectors.mapping(ObjectDataIdAndTagNameListData::getDataId, Collectors.toList()))));
        apiNameAndTagNameListAndObjectIdsMap.forEach((describeApiName, tagNameListAndObjectIdsMap) -> {
            tagNameListAndObjectIdsMap.forEach((tagNameList, objectIds) -> {
                addTagsToObjectDatas(ea, describeApiName, objectIds, tagNameList);
            });
        });
    }

    public void addTagsToObjectDatas(String ea, String describeApiName, List<String> objectIds, List<TagName> tagNameList) {
        log.info("addTagsToObjectDatas inner ea:{} describeApiName:{} objectIds:{} tagNames:{}", ea, describeApiName, objectIds, tagNameList);
        if (objectIds == null || objectIds.isEmpty()){
            return;
        }
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        Iterators.partition(objectIds.iterator(), 20).forEachRemaining(subObjectIds -> {
            Map<TagName, String> tagNameToPaasIdMap = this.getOrCreateTagIdsByTagNames(ea, describeApiName, tagNameList);
            Map<String, List<String>> dataIdToTagIdListMap = Maps.newHashMap();
            subObjectIds.forEach(objectId -> dataIdToTagIdListMap.put(objectId, new ArrayList<>(tagNameToPaasIdMap.values())));
            BulkHangTagForDataByIdArg bulkHangTagForDataByIdArg = new BulkHangTagForDataByIdArg(tenantId, describeApiName, dataIdToTagIdListMap);
            publicPurgeService.bulkAppendTagForData(bulkHangTagForDataByIdArg);
            // 标签打到企微
            Map<String, Set<TagName>> objectIdToReallyAddToOuterTagNames = new HashMap<>();
            subObjectIds.forEach(subObjectId -> objectIdToReallyAddToOuterTagNames.put(subObjectId, new HashSet<>(tagNameList)));
            if ((CrmObjectApiNameEnum.WECHAT.getName().equals(describeApiName) || CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(describeApiName)) && !objectIdToReallyAddToOuterTagNames.isEmpty()) {
                asyncAddOrRemoveDataTagToOuter(ea, describeApiName, objectIdToReallyAddToOuterTagNames, null);
            }
        });
    }


    public void updateTagsToObjectDatas(String ea, String describeApiName, List<String> objectIds, List<TagName> tagNameList) {
        log.info("updateTagsToObjectDatas inner ea:{} describeApiName:{} objectIds:{} tagNames:{}", ea, describeApiName, objectIds, tagNameList);
        if (objectIds == null || objectIds.isEmpty()){
            return;
        }
        deleteTagsToObjectDatas(ea, describeApiName, objectIds, tagNameList);
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        Iterators.partition(objectIds.iterator(), 20).forEachRemaining(subObjectIds -> {
            FindAllTagByBulkDataIdArg findAllTagByBulkDataIdArg = new FindAllTagByBulkDataIdArg(tenantId, describeApiName, subObjectIds);
            MetadataTagResult<List<DataIdAndMetadataTagData>> result = doFindAllTagByBulkDataId(findAllTagByBulkDataIdArg);
            if (!result.isSuccess()) {
                throw new OuterServiceRuntimeException(result.getErrCode(), result.getErrMessage());
            }
            if (result.getResult() != null) {
                if(tagNameList.isEmpty()){
                    return;
                }
                Map<TagName, String> tagNameToPaasIdMap = this.getOrCreateTagIdsByTagNames(ea, describeApiName, tagNameList);
                Map<String, TagName> paasIdToTagNameMap = tagNameToPaasIdMap.entrySet().stream().collect(Collectors.toMap(Entry::getValue, Entry::getKey, (v1, v2) -> v1));
                Collection<String> tagIdsToAdd = tagNameToPaasIdMap.values();
                final Map<String, Set<TagName>> objectIdToReallyAddTagNames = new HashMap<>();
                if (!tagIdsToAdd.isEmpty()) {
                    Map<String, List<String>> dataIdAndMetadataTagDataMap = Maps.newHashMap();
                    Map<String, List<String>> findAllTagByBulkDataIdMap = result.getResult().stream().collect(Collectors.toMap(DataIdAndMetadataTagData::getDataId, dataIdAndMetadataTagData ->
                            dataIdAndMetadataTagData.getResultList() == null ? new ArrayList<>()
                                    : dataIdAndMetadataTagData.getResultList().stream().map(MetadataTagData::getId).collect(Collectors.toList())));
                    Optional.ofNullable(findAllTagByBulkDataIdMap).ifPresent(val -> {
                        dataIdAndMetadataTagDataMap.putAll(findAllTagByBulkDataIdMap);
                    });
                    Map<String, List<String>> dataIdToTagIdListMap = subObjectIds.stream().collect(Collectors.toMap(val -> val, val -> dataIdAndMetadataTagDataMap.get(val) == null ? Lists.newArrayList() : dataIdAndMetadataTagDataMap.get(val) ));
                    Iterator<Entry<String, List<String>>> iterator = dataIdToTagIdListMap.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Entry<String, List<String>> entry = iterator.next();
                        if (entry.getValue().containsAll(tagIdsToAdd)) {
                            iterator.remove();
                        } else {
                            Set<String> bulkTagIdSet = new HashSet<>(tagIdsToAdd);
                            bulkTagIdSet.removeAll(entry.getValue());
                            Set<TagName> tagNamesToAdd = bulkTagIdSet.stream().map(paasIdToTagNameMap::get).collect(Collectors.toSet());
                            objectIdToReallyAddTagNames.put(entry.getKey(), tagNamesToAdd);
                            bulkTagIdSet.addAll(entry.getValue());
                            entry.setValue(new ArrayList<>(bulkTagIdSet));
                        }
                    }
                    BulkHangTagForDataByIdArg bulkHangTagForDataByIdArg = new BulkHangTagForDataByIdArg(tenantId, describeApiName, dataIdToTagIdListMap);
                    MetadataTagResult<Boolean> hangResult = metadataTagDataService.bulkHangTagForDataById(bulkHangTagForDataByIdArg);
                    log.info("bulkHangTagForDataById ea: {} params:{} result: {}", ea, bulkHangTagForDataByIdArg, hangResult);
                }
                if((CrmObjectApiNameEnum.WECHAT.getName().equals(describeApiName) || CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(describeApiName)) && !objectIdToReallyAddTagNames.isEmpty()){
                    asyncAddOrRemoveDataTagToOuter(ea, describeApiName, objectIdToReallyAddTagNames, null);
                }
            }
        });
    }

    public void deleteTagsToObjectDatas(String ea, String describeApiName, List<String> objectIds, List<TagName> tagNameList) {
        log.info("deleteTagsToObjectDatas inner ea:{} describeApiName:{} objectIds:{} tagNames:{}", ea, describeApiName, objectIds, tagNameList);
        if (objectIds == null || objectIds.isEmpty()){
            return;
        }
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        Iterators.partition(objectIds.iterator(), 20).forEachRemaining(subObjectIds -> {
            FindAllTagByBulkDataIdArg findAllTagByBulkDataIdArg = new FindAllTagByBulkDataIdArg(tenantId, describeApiName, subObjectIds);
            MetadataTagResult<List<DataIdAndMetadataTagData>> result = doFindAllTagByBulkDataId(findAllTagByBulkDataIdArg);
            if (!result.isSuccess()) {
                throw new OuterServiceRuntimeException(result.getErrCode(), result.getErrMessage());
            }
            if (result.getResult() != null) {
                GetCorpTagListArg getCorpTagListArg = new GetCorpTagListArg();
                GetCorpTagListResult getCorpTagListResult = qywxManager.getCorpTagList(ea, getCorpTagListArg);
                Map<TagName, String> tagNameToWxWorkTagIdMap = getCorpTagListResult.getAsTagNameToWxWorkTagIdMap();
                List<String> strings = tagNameToWxWorkTagIdMap.entrySet().stream().map(o -> o.getKey().getFirstTagName() +":"+ o.getKey().getSecondTagName()).collect(Collectors.toList());
                if(tagNameList.isEmpty()){
                    Map<String, List<String>> dataIdToTagIdListRemoveMap = result.getResult().stream().collect(Collectors.toMap(DataIdAndMetadataTagData::getDataId, dataIdAndMetadataTagData ->
                            dataIdAndMetadataTagData.getResultList() == null ? new ArrayList<>()
                                    : dataIdAndMetadataTagData.getResultList().stream().filter(o->strings.contains(o.getName())).map(MetadataTagData::getId).collect(Collectors.toList())));
                    BulkRemoveTagForDataByTagIdsArg arg = new BulkRemoveTagForDataByTagIdsArg(tenantId, describeApiName,dataIdToTagIdListRemoveMap);
                    MetadataTagResult<Boolean> removeResult = metadataTagDataService.bulkRemoveTagForDataByTagIds(arg);
                    log.info("bulkRemoveTagForDataByTagIds ea: {} params:{} result: {}", ea, arg, removeResult);
                    return;
                }
                Map<TagName, String> tagNameToPaasIdMap = this.getOrCreateTagIdsByTagNames(ea, describeApiName, tagNameList);
                Collection<String> tagIdsToAdd = tagNameToPaasIdMap.values();
                if (!tagIdsToAdd.isEmpty()) {
                    Map<String, List<String>> dataIdAndMetadataTagDataMap = Maps.newHashMap();
                    Map<String, List<String>> findAllTagByBulkDataIdMap = result.getResult().stream().collect(Collectors.toMap(DataIdAndMetadataTagData::getDataId, dataIdAndMetadataTagData ->
                            dataIdAndMetadataTagData.getResultList() == null ? new ArrayList<>()
                                    : dataIdAndMetadataTagData.getResultList().stream().filter(o->strings.contains(o.getName())).map(MetadataTagData::getId).collect(Collectors.toList())));
                    Optional.ofNullable(findAllTagByBulkDataIdMap).ifPresent(val -> {
                        dataIdAndMetadataTagDataMap.putAll(findAllTagByBulkDataIdMap);
                    });
                    Map<String, List<String>> dataIdToTagIdListMap = subObjectIds.stream().collect(Collectors.toMap(val -> val, val -> dataIdAndMetadataTagDataMap.get(val) == null ? Lists.newArrayList() : dataIdAndMetadataTagDataMap.get(val) ));
                    Iterator<Entry<String, List<String>>> iterator = dataIdToTagIdListMap.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Entry<String, List<String>> entry = iterator.next();
                        //回调信息移除用户标签
                        List<String> collect = entry.getValue().stream().filter(o -> o != null && !tagIdsToAdd.contains(o)).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(collect)){
                            Map<String, List<String>> dataIdToTagIdListRemoveMap = new HashMap<>();
                            dataIdToTagIdListRemoveMap.put(entry.getKey(),collect);
                            BulkRemoveTagForDataByTagIdsArg arg = new BulkRemoveTagForDataByTagIdsArg(tenantId, describeApiName,dataIdToTagIdListRemoveMap);
                            MetadataTagResult<Boolean> removeResult =  metadataTagDataService.bulkRemoveTagForDataByTagIds(arg);
                            log.info("bulkRemoveTagForDataByTagIds ea: {} params:{} result: {}", ea, arg, removeResult);
                        }
                    }
                }
            }
        });
    }
    public void asyncAddOrRemoveDataTagToOuter(String ea, String describeApiName, Map<String, Set<TagName>> objectIdToAddTagNames, Map<String, List<TagName>> objectIdToRemoveTagNames) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
            if (CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(describeApiName)){
                GetCorpTagListArg getCorpTagListArg = new GetCorpTagListArg();
                GetCorpTagListResult getCorpTagListResult = qywxManager.getCorpTagList(ea, getCorpTagListArg);
                if (getCorpTagListResult.isSuccess()){
                    Map<TagName, String> tagNameToWxWorkTagIdMap = getCorpTagListResult.getAsTagNameToWxWorkTagIdMap();
                    if(objectIdToAddTagNames != null){
                        doAddOrRemoveWxWorkDataTag(ea, objectIdToAddTagNames, tagNameToWxWorkTagIdMap, (enterpriseAccount, externalUserId, tagIds) -> {
                            return qywxManager.markTag(enterpriseAccount, externalUserId, tagIds, null);
                        });
                    }
                    if(objectIdToRemoveTagNames != null){
                        doAddOrRemoveWxWorkDataTag(ea, objectIdToRemoveTagNames, tagNameToWxWorkTagIdMap, (enterpriseAccount, externalUserId, tagIds) -> {
                            return qywxManager.markTag(enterpriseAccount, externalUserId, null, tagIds);
                        });
                    }
                }
            }
            if (CrmObjectApiNameEnum.WECHAT.getName().equals(describeApiName)) {
                Map<String, Map<TagName, Long>> multipleWxTagCache = new HashMap<>(5);
                if(objectIdToAddTagNames != null){
                    doAddOrRemoveWxFanDataTag(objectIdToAddTagNames, tenantId, multipleWxTagCache, (wxAppId, wxOpenId, tagId) -> {
                        TaggingArg taggingArg = new TaggingArg();
                        taggingArg.setOpenIdList(Lists.newArrayList(wxOpenId));
                        taggingArg.setTagId(tagId.intValue());
                        return wxFanTagService.taggingUser(wxAppId, taggingArg);
                    });
                }
                if(objectIdToRemoveTagNames != null){
                    doAddOrRemoveWxFanDataTag(objectIdToRemoveTagNames, tenantId, multipleWxTagCache, (wxAppId, wxOpenId, tagId) -> {
                        TaggingArg taggingArg = new TaggingArg();
                        taggingArg.setOpenIdList(Lists.newArrayList(wxOpenId));
                        taggingArg.setTagId(tagId.intValue());
                        return wxFanTagService.unTaggingUser(wxAppId, taggingArg);
                    });
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    private void doAddOrRemoveWxWorkDataTag(String ea, Map<String, ? extends Collection<TagName>> objectIdToActionTagNames, Map<TagName, String> tagNameToWxWorkTagIdMap, DoWxWorkTagDataAction doWxWorkTagDataAction) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        objectIdToActionTagNames.forEach((objectId, tagNames) -> {
            List<String> tagIdToAdd = tagNames.stream().map(tagName -> {
                String wxWorkTagId = tagNameToWxWorkTagIdMap.get(tagName);
//                if (wxWorkTagId == null && org.apache.commons.lang3.StringUtils.isNotBlank(tagName.getFirstTagName()) && org.apache.commons.lang3.StringUtils.isNotBlank(tagName.getSecondTagName())) {
//                    log.info("doAddOrRemoveWxWorkDataTag addCorpTag:{}", tagName);
//                    AddCorpTagArg addCorpTagArg = new AddCorpTagArg();
//                    addCorpTagArg.setGroupName(tagName.getFirstTagName());
//                    addCorpTagArg.setTags(Lists.newArrayList(new AddCorpTagArg.AddTagArg(tagName.getSecondTagName())));
//                    try {
//                        AddCorpTagResult addCorpTagResult = qywxManager.addCorpTag(ea, addCorpTagArg);
//                        if (addCorpTagResult != null && addCorpTagResult.isSuccess()) {
//                            wxWorkTagId = addCorpTagResult.getTagGroup().getTags().get(0).getId();
//                        }
//                    } catch (Exception e) {
//                        log.warn("Ex:", e);
//                    }
//                }
                return wxWorkTagId;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (!tagIdToAdd.isEmpty()) {
                try {
                    Result<ControllerGetDescribeResult> wechatWorkExternalUserIdObjectResult = wechatWorkExternalUserObjManager.getWechatWorkObjectDataById(tenantId, objectId, this);
                    if (wechatWorkExternalUserIdObjectResult.isSuccess()){
                        String externalUserId = wechatWorkExternalUserIdObjectResult.getData().getData().getString(WechatWorkExternalUserConstants.EXTERNAL_USER_ID);
                        if (externalUserId != null) {
                            doWxWorkTagDataAction.doAction(ea, externalUserId, tagIdToAdd);
                        }
                    }
                }catch (Exception e){
                    log.warn("Ex:", e);
                }
            }
        });
    }

    private void doAddOrRemoveWxFanDataTag(Map<String, ? extends Collection<TagName>> objectIdToAddTagNames, Integer tenantId, Map<String, Map<TagName, Long>> multipleWxTagCache, DoWxFanTagDataAction doWxFanTagDataAction) {
        objectIdToAddTagNames.forEach((objectId, tagNames) -> {
            Result<ControllerGetDescribeResult> dataResult = doGetWechatFanObjectDataById(tenantId, objectId);
            if (dataResult.isSuccess() && dataResult.getData() != null && dataResult.getData().getData() != null){
                String wxAppId = dataResult.getData().getData().getString(WechatFanFieldContants.WX_APP_ID);
                String wxOpenId = dataResult.getData().getData().getString(WechatFanFieldContants.WX_OPEN_ID);
                String appId = dataResult.getData().getData().getString(WechatFanFieldContants.APP_ID);
                if(wxAppId != null && wxOpenId != null && appId != null){
                    Map<TagName, Long> wxTagNameMap = multipleWxTagCache.get(wxAppId);
                    if (wxTagNameMap == null){
                        ModelResult<TagUsersResult> mr = wechatUserTagService.getWechatUserTags(wxAppId);
                        if (mr.isSuccess() && mr.getResult() != null && mr.getResult().getTags() != null){
                            multipleWxTagCache.put(wxAppId, mr.getResult().getTags().stream().collect(Collectors.toMap(tag -> new TagName(tag.getName(), null), Tag::getId, (v1, v2) -> v1)));
                        }
                        wxTagNameMap = multipleWxTagCache.get(wxAppId);
                    }
                    if(wxTagNameMap != null){
                        for (TagName tagName : tagNames) {
                            if (wxTagNameMap.get(tagName) != null){
                                doWxFanTagDataAction.doAction(wxAppId, wxOpenId, wxTagNameMap.get(tagName));
                            }
                        }
                    }
                }
            }
        });
    }

    @FunctionalInterface
    private interface DoWxWorkTagDataAction{
        boolean doAction(String ea, String externalUserId, List<String> tagIds);
    }

    @FunctionalInterface
    private interface DoWxFanTagDataAction {
        ModelResult<Boolean> doAction(String wxAppId, String wxOpenId, Long tagId);
    }

    private Result<ControllerGetDescribeResult> doGetWechatFanObjectDataById(Integer tenantId, String objectId){
        ControllerDetailArg wechatWorkExternalUserIdObjectArg = new ControllerDetailArg();
        wechatWorkExternalUserIdObjectArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT.getName());
        wechatWorkExternalUserIdObjectArg.setObjectDataId(objectId);
        return metadataControllerService.detail(new HeaderObj(tenantId, -10000), CrmObjectApiNameEnum.WECHAT.getName(), wechatWorkExternalUserIdObjectArg);
    }

    public MetadataTagResult<List<DataIdAndMetadataTagData>> doFindAllTagByBulkDataId(FindAllTagByBulkDataIdArg arg){
        if(arg.getDataIds() == null || arg.getDataIds().isEmpty()){
            return MetadataTagResult.newSuccess(new ArrayList<>(0));
        }
        MetadataTagResult<List<DataIdAndMetadataTagData>> rawResult = metadataTagDataService.findAllTagByBulkDataId(arg);
        if(!rawResult.isSuccess()){
            return rawResult;
        }
        if(rawResult.getResult() == null){
            rawResult.setResult(new ArrayList<>());
        }
        Set<String> allDataIds = new HashSet<>(arg.getDataIds());
        allDataIds.removeAll(rawResult.getResult().stream().map(DataIdAndMetadataTagData::getDataId).collect(Collectors.toList()));
        List<DataIdAndMetadataTagData> newResult = new ArrayList<>(rawResult.getResult());
        if(!allDataIds.isEmpty()){
            for (String dataId : allDataIds) {
                DataIdAndMetadataTagData dataIdAndMetadataTagData = new DataIdAndMetadataTagData();
                dataIdAndMetadataTagData.setDataId(dataId);
                dataIdAndMetadataTagData.setResultList(new ArrayList<>(0));
                newResult.add(dataIdAndMetadataTagData);
            }
        }
        rawResult.setResult(newResult);
        return rawResult;
    }

    private MetadataTagResult<Boolean> deleteTagsFromObjectDatas(String ea, String describeApiName, Map<String, List<TagName>> dataIdAndTagNameListMap) {
        List<ObjectDataIdAndTagNameListData> dataIdAndTagNameListObjectDataList = this
            .getObjectDataIdAndTagNameListDatasByObjectDataIds(ea, describeApiName, Lists.newArrayList(dataIdAndTagNameListMap.keySet()));
        if (CollectionUtils.isEmpty(dataIdAndTagNameListObjectDataList)) {
            return MetadataTagResult.newSuccess(true);
        }
        // 使用 Iterators.partition 进行分片处理
        Iterators.partition(dataIdAndTagNameListMap.entrySet().iterator(), 20)
                .forEachRemaining(partition -> {
                    Map<String, List<TagName>> currentPartitionMap = partition.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    //删除部分标签的数据
                    Map<String, List<TagName>> deleteAllThenToAddTagNameListMap = new HashMap<>();
                    //删除全部标签的数据
                    Map<String, List<TagName>> deleteAllTagMap = new HashMap<>();
                    for (ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData : dataIdAndTagNameListObjectDataList) {
                        String dataId = objectDataIdAndTagNameListData.getDataId();
                        if (!StringUtils.isNullOrEmpty(dataId) && currentPartitionMap.containsKey(dataId)) {
                            TagNameList tagNameList = objectDataIdAndTagNameListData.getTagNameList();
                            if (CollectionUtils.isNotEmpty(tagNameList)) {
                                List<TagName> needDeleteTagNameList = currentPartitionMap.get(dataId);
                                tagNameList.removeIf(needDeleteTagNameList::contains);
                                if (CollectionUtils.isEmpty(tagNameList)) {
                                    deleteAllTagMap.put(dataId, needDeleteTagNameList);
                                } else {
                                    deleteAllThenToAddTagNameListMap.put(dataId, tagNameList);
                                }
                            }
                        }
                    }
                    Map<String, List<String>> toDeleteAllThenToAddObjectIdAndTagIdsMap = Maps.newHashMap();
                    deleteAllThenToAddTagNameListMap.forEach((k, v) -> {
                        Map<TagName, String> tagNameAndTagIdMap = this.getOrCreateTagIdsByTagNames(ea, describeApiName, v);
                        List<String> tagIds = Lists.newArrayList(tagNameAndTagIdMap.values());
                        toDeleteAllThenToAddObjectIdAndTagIdsMap.put(k, tagIds);
                    });
                    Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
                    BulkHangTagForDataByIdArg arg = new BulkHangTagForDataByIdArg(tenantId, describeApiName, toDeleteAllThenToAddObjectIdAndTagIdsMap);
                    /**
                     * 这个接口会删除老的标签然后新增标签
                     */
                    metadataTagDataService.bulkHangTagForDataById(arg).getResult();
                    Map<String, List<String>> toDeleteAllObjectIdAndTagIdsMap = Maps.newHashMap();
                    deleteAllTagMap.forEach((k, v) -> {
                        Map<TagName, String> tagNameAndTagIdMap = this.getOrCreateTagIdsByTagNames(ea, describeApiName, v);
                        List<String> tagIds = Lists.newArrayList(tagNameAndTagIdMap.values());
                        toDeleteAllObjectIdAndTagIdsMap.put(k, tagIds);
                    });
                    BulkRemoveTagForDataByTagIdsArg bulkRemoveTagForDataByTagIdsArg = new BulkRemoveTagForDataByTagIdsArg(tenantId, describeApiName, toDeleteAllObjectIdAndTagIdsMap);
                    MetadataTagResult<Boolean> r = metadataTagDataService.bulkRemoveTagForDataByTagIds(bulkRemoveTagForDataByTagIdsArg);
                    if((CrmObjectApiNameEnum.WECHAT.getName().equals(describeApiName) || CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(describeApiName)) && !currentPartitionMap.isEmpty()) {
                        asyncAddOrRemoveDataTagToOuter(ea, describeApiName, null, currentPartitionMap);
                    }
                });

        return MetadataTagResult.newSuccess(true);
    }

    public void deleteTagsFromObjectDatas(String ea, List<ObjectDataIdAndTagNameListData> dataIdAndTagNameListObjectDataList) {
        Map<String, List<ObjectDataIdAndTagNameListData>> apiNameAndDataIdAndTagNameListDataMap = dataIdAndTagNameListObjectDataList.stream().filter(ObjectDataIdAndTagNameListData::valid)
            .collect(Collectors.groupingBy(ObjectDataIdAndTagNameListData::getApiName));
        for (Entry<String, List<ObjectDataIdAndTagNameListData>> apiNameAndDataIdAndTagNameListData : apiNameAndDataIdAndTagNameListDataMap.entrySet()) {
            String describeApiName = apiNameAndDataIdAndTagNameListData.getKey();
            List<ObjectDataIdAndTagNameListData> toDeleteDataIdAndTagNameListObjectDataList = apiNameAndDataIdAndTagNameListData.getValue();
            Map<String, List<TagName>> dataIdAndTagNameListMap = toDeleteDataIdAndTagNameListObjectDataList.stream()
                .collect(Collectors.toMap(ObjectDataIdAndTagNameListData::getDataId, ObjectDataIdAndTagNameListData::getTagNameList));
            this.deleteTagsFromObjectDatas(ea, describeApiName, dataIdAndTagNameListMap);
        }
    }

    public Map<String,ObjectDataIdAndTagNameListData> getObjectDataIdAndTagNameListDataMapByObjectDataIds(String ea, String describeApiName, List<String> objectDataIds){
        List<ObjectDataIdAndTagNameListData>  objectDataIdAndTagNameListDatas = this.getObjectDataIdAndTagNameListDatasByObjectDataIds(ea,describeApiName,objectDataIds);
        Map<String,ObjectDataIdAndTagNameListData> result = Maps.newHashMap();
        for (ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData : objectDataIdAndTagNameListDatas) {
            result.put(objectDataIdAndTagNameListData.getDataId(),objectDataIdAndTagNameListData);
        }
        return result;
    }

    //标签数据太多去除日志
//    @FilterLog
    public List<ObjectDataIdAndTagNameListData> getObjectDataIdAndTagNameListDatasByObjectDataIds(String ea, String describeApiName, List<String> objectDataIds) {
        if (CollectionUtils.isEmpty(objectDataIds)) {
            return null;
        }
        if (objectDataIds.size() > 1000) {
            // 一次性查太多标签数据内存顶不住
            log.warn("getObjectDataIdAndTagNameListDatasByObjectDataIds objectDataIds too much, describeApiName:{} size:{}", describeApiName, objectDataIds.size());
            return null;
        }
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        Set<String> tagIds = Sets.newHashSet();
        List<DataIdAndMetadataTagData> metadataTagDataList = Lists.newArrayList();
        Iterators.partition(objectDataIds.iterator(), 1000).forEachRemaining(subObjectDataIds -> {
            FindAllTagByBulkDataIdArg arg = new FindAllTagByBulkDataIdArg(tenantId, describeApiName, subObjectDataIds);
            MetadataTagResult<List<DataIdAndMetadataTagData>> result = doFindAllTagByBulkDataId(arg);
            if (!result.isSuccess()) {
                throw new OuterServiceRuntimeException(result.getErrCode(), result.getErrMessage());
            }
            List<DataIdAndMetadataTagData> dataIdAndMetadataTagDataList = result.getResult();
            if (CollectionUtils.isNotEmpty(dataIdAndMetadataTagDataList)) {
                for (DataIdAndMetadataTagData dataIdAndMetadataTagData : dataIdAndMetadataTagDataList) {
                    for (MetadataTagData metadataTagData : dataIdAndMetadataTagData.getResultList()) {
                        tagIds.add(metadataTagData.getId());
                    }
                }
                metadataTagDataList.addAll(dataIdAndMetadataTagDataList);
            }
        });
        Map<String, TagName> tagIdAndTagNameMap = this.getTagNamesByTagIds(ea, describeApiName, tagIds);
        List<ObjectDataIdAndTagNameListData> dataIdAndTagNameListObjectDataList = Lists.newArrayList();
        for (DataIdAndMetadataTagData dataIdAndMetadataTagData : metadataTagDataList) {
            String dataId = dataIdAndMetadataTagData.getDataId();
            TagNameList tagNameList = new TagNameList();
            for (MetadataTagData metadataTagData : dataIdAndMetadataTagData.getResultList()) {
                TagName tagName = tagIdAndTagNameMap.get(metadataTagData.getId());
                if (tagName != null) {
                    tagNameList.add(tagName);
                }
            }
            ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData = new ObjectDataIdAndTagNameListData(describeApiName, dataId, tagNameList);
            dataIdAndTagNameListObjectDataList.add(objectDataIdAndTagNameListData);
        }
        return dataIdAndTagNameListObjectDataList;
    }

    //批量给crm对象打标签, 新增标签&删除标签 -- 非追加打标签
    public void batchUpdateCrmDataTag(String ea, String crmObjectDescribeApiName, List<String> crmObjectIds, List<TagName> tagNameList) {
        if (CollectionUtils.isEmpty(crmObjectIds)) {
            return;
        }

        List<ObjectDataIdAndTagNameListData> tagNameListDataResult = metadataTagManager.getObjectDataIdAndTagNameListDatasByObjectDataIds(ea, crmObjectDescribeApiName, crmObjectIds);
        //全量删除标签
        if (CollectionUtils.isEmpty(tagNameList) && CollectionUtils.isNotEmpty(tagNameListDataResult)) {
            metadataTagManager.deleteTagsFromObjectDatas(ea,  tagNameListDataResult);
            return;
        }
        //全量新增
        if (CollectionUtils.isEmpty(tagNameListDataResult) && CollectionUtils.isNotEmpty(tagNameList)) {
            TagNameList addTagNameList = TagNameList.convert(tagNameList);
            List<ObjectDataIdAndTagNameListData> addTagCrmDataList = crmObjectIds.stream().map(crmObjectId -> new ObjectDataIdAndTagNameListData(crmObjectDescribeApiName, crmObjectId, addTagNameList)).collect(Collectors.toList());
            metadataTagManager.addTagsToObjectDatas(ea, addTagCrmDataList);
        }
        //无新增&无删除
        if (CollectionUtils.isEmpty(tagNameList) && CollectionUtils.isEmpty(tagNameListDataResult)) {
            return;
        }

        List<ObjectDataIdAndTagNameListData> addTagCrmDataList = Lists.newArrayList();
        List<ObjectDataIdAndTagNameListData> deleteTagCrmDataList = Lists.newArrayList();
        Map<String, ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListDataMap = tagNameListDataResult.stream().collect(Collectors.toMap(ObjectDataIdAndTagNameListData::getDataId, Function.identity()));
        for (Map.Entry<String, ObjectDataIdAndTagNameListData> entry : objectDataIdAndTagNameListDataMap.entrySet()) {
            String objectDataId = entry.getKey();
            ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData = entry.getValue();
            //比较tagNames和currentObjectDataTags，删除不在tagNames中的标签, 新增不在currentObjectDataTags中的标签
            TagNameList currentObjectDataTags = objectDataIdAndTagNameListData.getTagNameList();
            List<TagName> addTags = tagNameList.stream().filter(tagName -> !currentObjectDataTags.contains(tagName)).collect(Collectors.toList());
            List<TagName> deleteTags = currentObjectDataTags.stream().filter(tagName -> !tagNameList.contains(tagName)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(addTags)) {
                ObjectDataIdAndTagNameListData addTagCrmData = new ObjectDataIdAndTagNameListData(crmObjectDescribeApiName, objectDataId, TagNameList.convert(addTags));
                addTagCrmDataList.add(addTagCrmData);
            }
            if (CollectionUtils.isNotEmpty(deleteTags)) {
                ObjectDataIdAndTagNameListData deleteTagCrmData = new ObjectDataIdAndTagNameListData(crmObjectDescribeApiName, objectDataId, TagNameList.convert(deleteTags));
                deleteTagCrmDataList.add(deleteTagCrmData);
            }
        }

        if (CollectionUtils.isNotEmpty(addTagCrmDataList)) {
            metadataTagManager.addTagsToObjectDatas(ea, addTagCrmDataList);
        }
        if (CollectionUtils.isNotEmpty(deleteTagCrmDataList)) {
            metadataTagManager.deleteTagsFromObjectDatas(ea, deleteTagCrmDataList);
        }
    }

    public List<String> getObjectDataIdsByTagNameList(String ea, Integer fsUserId, String describeApiName, String operator, List<TagName> tagNames, int offset, int limit) {
        List<String> objectIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tagNames)) {
            Map<TagName, String> tagNameTagIdMap = metadataTagManager.getTagIdsByTagNames(ea, describeApiName, tagNames);
            if (MapUtils.isEmpty(tagNameTagIdMap)) {
                return objectIds;
            }
            List<String> tagIds = Lists.newArrayList(tagNameTagIdMap.values());
            tagIds.removeIf(StringUtils::isNullOrEmpty);
            if (CollectionUtils.isEmpty(tagIds)) {
                return objectIds;
            }
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            paasQueryFilterArg.setObjectAPIName(describeApiName);
            PaasQueryArg query = new PaasQueryArg(0, 1);
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("tag", tagIds, "HASANYOF");
            condition.setValueType(11);
            query.setFilters(Lists.newArrayList(condition));
            paasQueryFilterArg.setQuery(query);
            crmV2Manager.listCrmObjectScanByIdAndHandle(ea, fsUserId, paasQueryFilterArg, 1000, e -> objectIds.add(e.getId()));
        }
        return objectIds;
    }

    public void bulkHangTag(String ea, Integer fsUserId, String objectApiName, List<String> objectDataIds, List<String> tagIds) {
        if (CollectionUtils.isEmpty(objectDataIds) || CollectionUtils.isEmpty(tagIds)) {
            return;
        }

        HeaderObj headerObj = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId);
        BulkHangTagArg bulkHangTagArg = new BulkHangTagArg();
        bulkHangTagArg.setTagIds(tagIds);
        bulkHangTagArg.setDataIds(objectDataIds);
        bulkHangTagArg.setAppend(true);
        tagRestService.bulkHangTag(headerObj, objectApiName, bulkHangTagArg);
    }
}
