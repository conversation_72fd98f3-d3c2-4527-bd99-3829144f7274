package com.facishare.marketing.provider.innerArg.qywx;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerGroupListArg implements Serializable {
    @SerializedName("owner_filter")
    private OwnerFilter ownerFilter;

    @SerializedName("cursor")
    private String cursor;              //分页，偏移量

    @SerializedName("limit")
    private int limit;               //分页，预期请求的数据量，取值范围 1 ~ 1000

    @Data
    public static class OwnerFilter implements Serializable{
        @SerializedName("userid_list")
        private List<String> userIds;

        @SerializedName("partyid_list")
        private List<Integer> departmentIds;
    }
}
