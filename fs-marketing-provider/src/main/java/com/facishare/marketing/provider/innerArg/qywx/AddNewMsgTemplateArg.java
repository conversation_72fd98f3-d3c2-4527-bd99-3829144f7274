package com.facishare.marketing.provider.innerArg.qywx;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 19:45 2020/2/6
 * @ModifyBy
 */
@Data
public class AddNewMsgTemplateArg implements Serializable {

    @SerializedName("chat_type")
    private String chatType;

    @SerializedName("external_userid")
    private List<String> externalUserid;//客户的外部联系人id列表，不可与sender同时为空，最多可传入1万个客户

    @SerializedName("attachments")
    private List<Attachments> attachments;

    @SerializedName("sender")
    private String sender;//发送企业群发消息的成员userid，不可与external_userid同时为空

    @SerializedName("chat_id_list")
    private List<String> chatIdList;//客户群id列表，仅在chat_type为group时有效，最多可一次指定2000个客户群。指定群id之后，收到任务的群主无须再选择客户群

    @SerializedName("text")
    private Text text;

    @SerializedName("allow_select")
    private Boolean allowSelect;

    @Data
    public static class Attachments implements Serializable{
        @SerializedName("msgtype")
        private String msgType;
        @SerializedName("image")
        private Image image;
        @SerializedName("link")
        private Link link;
        @SerializedName("miniprogram")
        private Miniprogram miniprogram;
        @SerializedName("video")
        private Video video; //视频
        @SerializedName("file")
        private File file; //文件
    }

    @Data
    public static class Text implements Serializable {
        @SerializedName("content")
        private String content; //消息文本内容，最多4000个字节
    }

    @Data
    public static class Image implements Serializable {
        @SerializedName("media_id")
        private String mediaId;//图片的media_id
    }

    @Data
    public static class Link implements Serializable {
        @SerializedName("title")
        private String title;//图文消息标题
        @SerializedName("picurl")
        private String picUrl;//图文消息封面的url
        @SerializedName("desc")
        private String desc;//图文消息的描述，最多512个字节
        @SerializedName("url")
        private String url;//图文消息的链接
        private boolean appendMktParam;
    }

    @Data
    public static class Miniprogram implements Serializable {
        @SerializedName("title")
        private String title;//小程序消息标题，最多64个字节
        @SerializedName("pic_media_id")
        private String picMediaId;//小程序消息封面的mediaid，封面图建议尺寸为520*416
        @SerializedName("appid")
        private String appId;//小程序appid，必须是关联到企业的小程序应用
        @SerializedName("page")
        private String page;//小程序page路径
        private boolean appendMktParam;
    }

    @Data
    public static class Video implements Serializable{
        @SerializedName("media_id")
        private String mediaId;
    }

    @Data
    public static class File implements Serializable{
        @SerializedName("media_id")
        private String mediaId;
    }
}
