package com.facishare.marketing.provider.dao.card;

import com.facishare.marketing.provider.entity.CardTemplateEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/24
 **/
public interface CardTemplateDao {

    @Insert("INSERT INTO card_template " +
            "(id, name, ea, is_default, card_common_setting, create_time, update_time) " +
            "values " +
            "(#{obj.id}, #{obj.name}, #{obj.ea}, #{obj.isDefault}, #{obj.cardCommonSetting}, now(), now()) ")
    int insert(@Param("obj") CardTemplateEntity entity);

    @Update("UPDATE card_template SET name=#{obj.name}, card_common_setting=#{obj.cardCommonSetting, typeHandler = com.facishare.marketing.common.typehandlers.CardCommonSettingListTypeHandler}, update_time=now() WHERE id=#{obj.id}")
    int update(@Param("obj") CardTemplateEntity entity);

    @Update("UPDATE card_template SET update_time=now() WHERE id = #{id}")
    int updateTime(@Param("id") String id);

    @Select("SELECT * FROM card_template WHERE id = #{id}")
    CardTemplateEntity getById(@Param("id") String id);

    @Select("SELECT * FROM card_template WHERE ea = #{ea} order by update_time desc ")
    List<CardTemplateEntity> getByEa(@Param("ea") String ea);

    @Select("SELECT * FROM card_template WHERE ea = #{ea} and is_default = 1")
    CardTemplateEntity getDefault(@Param("ea") String ea);

    @Delete("delete FROM card_template WHERE id = #{id}")
    int deleteById(@Param("id") String id);

}
