package com.facishare.marketing.provider.innerArg.qywx;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by z<PERSON>gh on 2020/12/10.
 */
@Data
public class UpdateContactMeConfigArg implements Serializable{
    @SerializedName("config_id")
    private String configId;          //企业联系方式的配置id

    @SerializedName("remark")
    private String remark;            //联系方式的备注信息，不超过30个字符，将覆盖之前的备注

    @SerializedName("skip_verify")
    private boolean skipVerify;   //外部客户添加时是否无需验证，默认为true

    @SerializedName("style")
    private Integer style;         //在小程序中联系时使用的控件样式，详见附表

    @SerializedName("state") //企业自定义的state参数，用于区分不同的添加渠道，在调用“获取外部联系人详情”时会返回该参数值，不超过30个字符
    private String state;

    @SerializedName("user")
    private List<String> user;  //使用该联系方式的用户userID列表，在type为1时为必填，且只能有一个

    @SerializedName("party")    //使用该联系方式的部门id列表，只在type为2时有效
    private List<Integer> party;

    @SerializedName("expiresIn")
    private Integer expires_in;   //临时会话二维码有效期，以秒为单位。该参数仅在is_temp为1时有效，默认7天

    @SerializedName("chat_expires_in")
    private Integer chatExpiresIn;  //临时会话有效期，以秒为单位。该参数仅在is_temp为1时有效，默认为添加好友后24小时

    public UpdateContactMeConfigArg(String remark, List<String> users, String state){
        this.remark = remark;
        this.user = users;
        this.state = state;
        this.style = 2;
        this.skipVerify = true;
    }
}
