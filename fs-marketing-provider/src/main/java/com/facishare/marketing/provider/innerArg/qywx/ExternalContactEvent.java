package com.facishare.marketing.provider.innerArg.qywx;

import com.facishare.open.msg.model.base.ProtoBase;
import io.protostuff.Tag;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class ExternalContactEvent extends ProtoBase implements Serializable {
    @Tag(1)
    private String appId;   //应用appId

    @Tag(2)
    private String corpId;   //企微corpId

    @Tag(3)
    private String fsEa;    //纷享ea

    @Tag(4)
    private String changeType;   //客户事件类型

    @Tag(5)
    private String userId;    //企微员工id

    @Tag(6)
    private String fsUserId;   //纷享员工id 该字段已经废弃 不要用

    @Tag(7)
    private String externalUserId;//外部联系人的userid，注意不是企业成员的帐号

    @Tag(8)
    private String externalContactDetail; //从企业微信外部联系人详情接口拿回来的原始JSON数据

    @Tag(9)
    private Map<String, String> userIdMap;  //人员id映射
}