/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.remote;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.CrmConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.CrmFieldResult;
import com.facishare.marketing.api.result.EnumDetailResult;
import com.facishare.marketing.api.result.GetRelatedDataListResult;
import com.facishare.marketing.common.contstant.CrmStatusMessageConstant;
import com.facishare.marketing.common.contstant.CustomizeFormDataConstants;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.campaign.CampaignConstants;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.crm.*;
import com.facishare.marketing.common.enums.qywx.AppScopeEnum;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.typehandlers.value.RuleGroupList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.entity.usermarketingaccount.*;
import com.facishare.marketing.provider.innerArg.crm.AggregateParameterArg;
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult;
import com.facishare.marketing.provider.innerResult.crm.CreateObjResult;
import com.facishare.marketing.provider.innerResult.qywx.GetExternalContactDetailResult;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.paas.crm.vo.base.CrmDuplicateSearchVo;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.open.app.pay.api.service.QuotaService;
import com.facishare.open.common.result.BaseResult;
import com.facishare.open.emailproxy.common.thread.ThreadUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.common.result.CheckPrivilegeResult;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.*;
import com.fxiaoke.crmrestapi.result.ObjectRecordTypeFindRecordTypeListResult.ObjectRecord;
import com.fxiaoke.crmrestapi.service.*;
import com.fxiaoke.paasauthrestapi.arg.FuncPermissionCheckArg;
import com.fxiaoke.paasauthrestapi.service.PaasAuthService;
import com.fxiaoke.retrofit2.http.Body;
import com.fxiaoke.retrofit2.http.HeaderMap;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CrmV2Manager {
    @Autowired
    private EIEAConverter eIEAConverter;
    @Autowired
    private QuotaService quotaService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private DuplicatesearchService duplicatesearchService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private ObjectRecordTypeService objectRecordTypeService;
    @Autowired
    private PaasAuthService paasAuthService;
    @Autowired
    private LeadsPoolService leadsPoolService;
    @Autowired
    private ObjectService objectService;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @ReloadableProperty("crm.appid")
    private String crmAppId;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;
    @Autowired
    private UserMarketingCrmAccountAccountRelationDao userMarketingCrmAccountAccountRelationDao;
    @Autowired
    private UserMarketingCrmContactAccountRelationDao userMarketingCrmContactAccountRelationDao;
    @Autowired
    private UserMarketingCrmWxUserAccountRelationDao userMarketingCrmWxUserAccountRelationDao;
    @Autowired
    private UserMarketingCrmMemberRelationDao userMarketingCrmMemberRelationDao;
    @Autowired
    private UserMarketingCrmWxWorkExternalUserRelationDao userMarketingCrmWxWorkExternalUserRelationDao;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;

    private final Cache<String, String> OBJECT_ID_TO_NAME_CACHE = CacheBuilder.newBuilder().maximumSize(100000).expireAfterWrite(12, TimeUnit.HOURS).build();
    
    private final Cache<String, String> WX_USER_TO_NAME_CACHE = CacheBuilder.newBuilder().maximumSize(100000).expireAfterWrite(12, TimeUnit.DAYS).build();
    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    public Optional<ObjectData> getWechatFanByOpenId(String ea, String wxAppId, String wxOpenId){
        try {
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.addFilter(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName(), Lists.newArrayList(wxAppId), FilterOperatorEnum.EQ);
            searchQuery.addFilter(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName(), Lists.newArrayList(wxOpenId), FilterOperatorEnum.EQ);
            searchQuery.setOffset(0);
            searchQuery.setLimit(20);
            Page<ObjectData> p = getList(ea, -10000, CrmObjectApiNameEnum.WECHAT.getName(), searchQuery);
            if (p.getDataList() != null && !p.getDataList().isEmpty()){
                return Optional.of(p.getDataList().get(0));
            }
        } catch (Exception e) {
            log.warn("Ex", e);
        }
        return Optional.empty();
    }

    public Map<String, ObjectData> getWechatFanByOpenIds(String ea, String wxAppId, List<String> wxOpenIds) {
        Map<String, ObjectData> resultMap = Maps.newHashMap();
        try {
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.addFilter(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName(), Lists.newArrayList(wxAppId), FilterOperatorEnum.EQ);
            searchQuery.addFilter(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName(), wxOpenIds, FilterOperatorEnum.IN);
            searchQuery.setOffset(0);
            searchQuery.setLimit(1000);
            Page<ObjectData> dataPage = getList(ea, -10000, CrmObjectApiNameEnum.WECHAT.getName(), searchQuery);
            if (dataPage == null || CollectionUtils.isEmpty(dataPage.getDataList())) {
                return resultMap;
            }
            List<ObjectData> dataList = dataPage.getDataList();
            for (ObjectData objectData : dataList) {
                String wxOpenId = objectData.getString(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName());
                if (StringUtils.isNotBlank(wxOpenId)) {
                    resultMap.put(wxOpenId, objectData);
                }
            }
        } catch (Exception e) {
            log.warn("CrmV2Manager.getWechatFanByOpenIds error e:{}", e);
        }
        return resultMap;
    }

    public List<ObjectData> getWechatFanByUnionId(String ea, String unionId) {
        List<ObjectData> objectDataList = Lists.newArrayList();
        try {
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.addFilter(CrmWechatFanFieldEnum.WX_UNION_ID.getFieldName(), Lists.newArrayList(unionId), FilterOperatorEnum.EQ);
            searchQuery.setOffset(0);
            searchQuery.setLimit(1000);
            Page<ObjectData> dataPage = getList(ea, -10000, CrmObjectApiNameEnum.WECHAT.getName(), searchQuery);
            if (dataPage == null || CollectionUtils.isEmpty(dataPage.getDataList())) {
                return objectDataList;
            }
            return dataPage.getDataList();
        } catch (Exception e) {
            log.warn("CrmV2Manager.getWechatFanByUnionId error e:{}", e);
        }
        return objectDataList;
    }

    public List<ObjectData> getWxExternalUserByUnionId(String ea, String unionId) {
        List<ObjectData> objectDataList = Lists.newArrayList();
        try {
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.addFilter(CrmWechatWorkExternalUserFieldEnum.WX_UNION_ID.getFieldName(), Lists.newArrayList(unionId), FilterOperatorEnum.EQ);
            searchQuery.setOffset(0);
            searchQuery.setLimit(1000);
            Page<ObjectData> dataPage = getList(ea, -10000, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), searchQuery);
            if (dataPage == null || CollectionUtils.isEmpty(dataPage.getDataList())) {
                return objectDataList;
            }
            return dataPage.getDataList();
        } catch (Exception e) {
            log.warn("CrmV2Manager.getWechatFanByUnionId error e:{}", e);
        }
        return objectDataList;
    }

    public void doAddTeamMemberToCrm(HeaderObj addHeader, Collection<Integer> fsUserToTeamMember, String apiName, String objectId) {
        if (CollectionUtils.isEmpty(fsUserToTeamMember)) {
            return;
        }
        AddTeamMemberArg addTeamMemberArg = new AddTeamMemberArg();
        addTeamMemberArg.setDataIDs(Collections.singletonList(objectId));
        List<String> fsUserToTeamMemberStr = fsUserToTeamMember.stream().map(Object::toString).collect(Collectors.toList());
        addTeamMemberArg.setTeamMemberEmployee(fsUserToTeamMemberStr);
        addTeamMemberArg.setTeamMemberPermissionType("2");
        addTeamMemberArg.addTeamMemberRole("4");
        addTeamMemberArg.setIgnoreSendingRemind(true);
        Result<Void> result = metadataActionService.addTeamMember(addHeader, apiName, addTeamMemberArg);
        log.info("doAddTeamMemberToCrm addHeader: {} addTeamMemberArg: {} result: {}", addHeader, addTeamMemberArg, result);
    }

    public void replaceTeamMember(String ea, Collection<Integer> fsUserToTeamMember, String objectId, String objectApiName) {
        ObjectData objectData = getDetailIgnoreError(ea, SuperUserConstants.USER_ID, objectApiName, objectId);
        if (objectData == null) {
            log.info("replaceTeamMember ea: {} objectApiName: {} objectId: {} objectData is null", ea, objectApiName, objectId);
            return;
        }
        int ei = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
        GetTeamMemberArg getTeamMemberArg = new GetTeamMemberArg();
        getTeamMemberArg.setObjectDescribeApiName(objectApiName);
        getTeamMemberArg.setDataID(objectId);
        Result<GetTeamMemberResult> getTeamMemberResult = metadataControllerService.getTeamMember(systemHeader, getTeamMemberArg);
        log.info("replaceTeamMember ea: {} objectId: {} getTeamMemberResult: {}", ea, objectId, getTeamMemberResult);
        if (!getTeamMemberResult.isSuccess() || getTeamMemberResult.getData() == null) {
            log.warn("replaceTeamMember ea: {} objectId: {} getTeamMember error", ea, objectId);
            return;
        }
        Set<Integer> crmExistedTeamMemberSet = getTeamMemberResult.getData().getAllTeamMember();
        Set<Integer> newTeamMemberIdSet = Sets.newHashSet(fsUserToTeamMember);
        // 新增相关团队成员
        Set<Integer> addTeamMemberIdSet = newTeamMemberIdSet.stream().filter(id -> !crmExistedTeamMemberSet.contains(id)).collect(Collectors.toSet());
        doAddTeamMemberToCrm(new HeaderObj(ei, SuperUserConstants.USER_ID), Lists.newArrayList(addTeamMemberIdSet), objectApiName, objectId);
        // 删除相关团队成员
        Integer owner = objectData.getOwner();
        Set<Integer> deleteTeamMemberIdSet = crmExistedTeamMemberSet.stream().filter(id -> !newTeamMemberIdSet.contains(id)).collect(Collectors.toSet());
        // 负责人不能删除
        deleteTeamMemberIdSet.remove(owner);
        doRemoveTeamMemberToCrm(new HeaderObj(ei, SuperUserConstants.USER_ID), Lists.newArrayList(deleteTeamMemberIdSet), objectApiName, objectId);
        log.info("replaceTeamMember ea: {} objectId: {} addTeamMemberIdSet: {} deleteTeamMemberIdSet: {}", ea, objectId, addTeamMemberIdSet, deleteTeamMemberIdSet);
    }


    //teamMemberRole:1-负责人 4-普通人员 teamMemberPermissionType:2-读写 1-只读
    public Result<Void> doAddTeamMemberToCrm(String ea, int operator, Collection<Integer> fsUserToTeamMember,
                                     List<String> objectIdList, String objectApiName, List<String> teamMemberRoleList, String teamMemberPermissionType) {
        if (CollectionUtils.isEmpty(fsUserToTeamMember) || CollectionUtils.isEmpty(objectIdList)) {
            Result<Void> result = new Result<>();
            result.setCode(-1);
            result.setMessage(I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_6));
            return result;
        }
        HeaderObj addHeader = createHeaderObj(ea, operator);
        AddTeamMemberArg addTeamMemberArg = new AddTeamMemberArg();
        addTeamMemberArg.setDataIDs(objectIdList);
        List<String> fsUserToTeamMemberStr = fsUserToTeamMember.stream().map(String::valueOf).distinct().collect(Collectors.toList());
        addTeamMemberArg.setTeamMemberEmployee(fsUserToTeamMemberStr);
        addTeamMemberArg.setTeamMemberPermissionType(teamMemberPermissionType);
        addTeamMemberArg.setTeamMemberRoleList(teamMemberRoleList);
        addTeamMemberArg.setIgnoreSendingRemind(true);
        return metadataActionService.addTeamMember(addHeader, objectApiName, addTeamMemberArg);
    }

    public void doRemoveTeamMemberToCrm(HeaderObj addHeader, Collection<Integer> fsUserToTeamMember, String apiName, String objectId) {
        DeleteTeamMemberArg deleteTeamMemberArg = new DeleteTeamMemberArg();
        deleteTeamMemberArg.setDataIDs(Collections.singletonList(objectId));
        List<String> fsUserToTeamMemberStr = fsUserToTeamMember.stream().map(Object::toString).collect(Collectors.toList());
        deleteTeamMemberArg.setTeamMemberEmployee(fsUserToTeamMemberStr);
        metadataActionService.deleteTeamMember(addHeader, apiName, deleteTeamMemberArg);
    }

    public void doRemoveTeamMemberToCrm(String ea, int operator, Collection<Integer> fsUserId, String objectId, String objectApiName) {
        HeaderObj headerObj = createHeaderObj(ea, operator);
        DeleteTeamMemberArg deleteTeamMemberArg = new DeleteTeamMemberArg();
        deleteTeamMemberArg.setDataIDs(Collections.singletonList(objectId));
        List<String> fsUserToTeamMemberStr = fsUserId.stream().map(Object::toString).collect(Collectors.toList());
        deleteTeamMemberArg.setTeamMemberEmployee(fsUserToTeamMemberStr);
        metadataActionService.deleteTeamMember(headerObj, objectApiName, deleteTeamMemberArg);
    }

    public Optional<String> getWxNickNameByCache(String ea, String wxAppId, String wxOpenId){
        if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(wxAppId) || Strings.isNullOrEmpty(wxOpenId)){
            return Optional.empty();
        }
        String name = WX_USER_TO_NAME_CACHE.getIfPresent(wxAppId + ":" + wxOpenId);
        if (!Strings.isNullOrEmpty(name)){
            return Optional.of(name);
        }
        Optional<ObjectData> wxUserOptional = this.getWechatFanByOpenId(ea, wxAppId, wxOpenId);
        if(wxUserOptional.isPresent() && !Strings.isNullOrEmpty(wxUserOptional.get().getName())){
            WX_USER_TO_NAME_CACHE.put(wxAppId + ":" + wxOpenId, wxUserOptional.get().getName());
            return Optional.of(wxUserOptional.get().getName());
        }
        return Optional.empty();
    }

    public Set<String> getObjectFieldNameList(String ea, CrmObjectApiNameEnum crmObjectApiNameEnum) {
        Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null), crmObjectApiNameEnum.getName());
        Set<String> requiredFieldNames = new HashSet<>();
        for (String key : result.getData().getDescribe().getFields().keySet()) {
            FieldDescribe fieldDescribe = result.getData().getDescribe().getFields().get(key);
            if (fieldDescribe.isRequired()) {
                if (objectFieldDescribesFilter(crmObjectApiNameEnum, fieldDescribe)) {
                    continue;
                }
                // 去除作废字段
                if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
                    continue;
                }
                // 过滤无法处理字段
                if (StringUtils.isNotBlank(fieldDescribe.getApiName()) && CrmV2FieldTypeEnum.IllegalApiName(fieldDescribe.getApiName())) {
                    continue;
                }
                // 过滤归属组织
                if (StringUtils.isNotBlank(fieldDescribe.getApiName()) && CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName().equals(fieldDescribe.getApiName())) {
                    continue;
                }
                //过滤自动编号字段
                if (Objects.equals(CrmV2FieldTypeEnum.Auto_Number.getName(),fieldDescribe.getType())) {
                    continue;
                }
                requiredFieldNames.add(key);
            }
        }

        return requiredFieldNames;
    }

    public List<CrmUserDefineFieldVo> getObjectFieldDescribesList(String ea, CrmObjectApiNameEnum crmObjectApiNameEnum) {
        Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null), crmObjectApiNameEnum.getName());
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = new ArrayList<>();
        for (Map.Entry<String, FieldDescribe> entry : result.getData().getDescribe().getFields().entrySet()) {
            FieldDescribe fieldDescribe = entry.getValue();
            if (objectFieldDescribesFilter(crmObjectApiNameEnum, fieldDescribe)) {
                continue;
            }

            // 过滤系统字段
            if (crmObjectApiNameEnum == CrmObjectApiNameEnum.MEMBER){
                if("system".equals(fieldDescribe.getDefineType()) && !"name".equals(fieldDescribe.getApiName())){
                    continue;
                }
                ImmutableSet<String> excludeFields = ImmutableSet.of("lock_rule", "extend_obj_data_id", "life_status_before_invalid", "owner_department", "integral_value", "area_location", "lock_status",
                    "growth_value", "relevant_team", "lock_user", "grade_id", "owner", "life_status", "record_type", "customer_id", "location");
                if (excludeFields.contains(fieldDescribe.getApiName())){
                    continue;
                }
            }

            // 去除作废字段
            if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
                continue;
            }

            // 过滤无法处理字段
            if (StringUtils.isNotBlank(fieldDescribe.getApiName()) && CrmV2FieldTypeEnum.IllegalApiName(fieldDescribe.getApiName())) {
                continue;
            }
            if (Objects.equals(CrmV2FieldTypeEnum.Auto_Number.getName(),fieldDescribe.getType())) {
                continue;
            }

            CrmUserDefineFieldVo crmUserDefineFieldVo = fieldDescribeToDefineVo(fieldDescribe);
            crmUserDefineFieldVo.setFieldTypeName(fieldDescribe.getType());
            crmUserDefineFieldVoList.add(crmUserDefineFieldVo);
        }

        return crmUserDefineFieldVoList;
    }

    public Set<String> getObjectFieldNameListByApiName(String ea, String crmApiName) {
        Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null), crmApiName);
        Set<String> requiredFieldNames = new HashSet<>();
        for (String key : result.getData().getDescribe().getFields().keySet()) {
            FieldDescribe fieldDescribe = result.getData().getDescribe().getFields().get(key);
            if (fieldDescribe.isRequired()) {
                if (objectFieldDescribesFilterByObjectApiName(crmApiName, fieldDescribe)) {
                    continue;
                }
                // 去除作废字段
                if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
                    continue;
                }
                // 过滤无法处理字段
                if (StringUtils.isNotBlank(fieldDescribe.getApiName()) && CrmV2FieldTypeEnum.IllegalApiName(fieldDescribe.getApiName())) {
                    continue;
                }
                // 过滤归属组织
                if (StringUtils.isNotBlank(fieldDescribe.getApiName()) && CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName().equals(fieldDescribe.getApiName())) {
                    continue;
                }
                if (Objects.equals(CrmV2FieldTypeEnum.Auto_Number.getName(),fieldDescribe.getType())) {
                    continue;
                }
                requiredFieldNames.add(key);
            }
        }

        return requiredFieldNames;
    }

    public boolean checkObjectFieldExist(String ea, String objectApiName, String fieldName) {
        Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null),objectApiName);
        if (result == null || !result.isSuccess() || result.getData() == null || result.getData().getDescribe() == null || result.getData().getDescribe().getFields() == null) {
            return false;
        }
        for (Map.Entry<String, FieldDescribe> entry : result.getData().getDescribe().getFields().entrySet()) {
            FieldDescribe fieldDescribe = entry.getValue();
            if (fieldDescribe.getApiName() != null && fieldDescribe.getApiName().equals(fieldName)) {
                return true;
            }
        }
        return false;
    }

    public List<CrmUserDefineFieldVo> getObjectFieldDescribesListByObjectApiName(String ea, String objectApiName) {
        Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null),objectApiName);
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = new ArrayList<>();
        if (!result.isSuccess()) {
            log.error("CrmV2Manager.getObjectFieldDescribesListByObjectApiName error, objectApiName:{}, result:{}", objectApiName, result);
            return crmUserDefineFieldVoList;
        }
        for (Map.Entry<String, FieldDescribe> entry : result.getData().getDescribe().getFields().entrySet()) {
            FieldDescribe fieldDescribe = entry.getValue();
            if (objectFieldDescribesFilterByObjectApiName(objectApiName, fieldDescribe)) {
                continue;
            }

            // 过滤系统字段
            if (objectApiName.equals(CrmObjectApiNameEnum.MEMBER.getName())){
                if("system".equals(fieldDescribe.getDefineType()) && !"name".equals(fieldDescribe.getApiName())){
                    continue;
                }
                ImmutableSet<String> excludeFields = ImmutableSet.of("lock_rule", "extend_obj_data_id", "life_status_before_invalid", "owner_department", "integral_value", "area_location", "lock_status",
                        "growth_value", "relevant_team", "lock_user", "grade_id", "owner", "life_status", "record_type", "customer_id", "location");
                if (excludeFields.contains(fieldDescribe.getApiName())){
                    continue;
                }
            }

            // 去除作废字段
            if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
                continue;
            }

            // 过滤无法处理字段
            if (StringUtils.isNotBlank(fieldDescribe.getApiName()) && CrmV2FieldTypeEnum.IllegalApiName(fieldDescribe.getApiName())) {
                continue;
            }

            if (Objects.equals(CrmV2FieldTypeEnum.Auto_Number.getName(),fieldDescribe.getType())) {
                continue;
            }

            CrmUserDefineFieldVo crmUserDefineFieldVo = fieldDescribeToDefineVo(fieldDescribe);
            crmUserDefineFieldVo.setFieldTypeName(fieldDescribe.getType());
            crmUserDefineFieldVoList.add(crmUserDefineFieldVo);
        }

        return crmUserDefineFieldVoList;
    }

    public List<CrmUserDefineFieldVo> getObjectFieldDescribesListWithLowFilter(String ea, CrmObjectApiNameEnum crmObjectApiNameEnum) {
        Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null), crmObjectApiNameEnum.getName());
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = new ArrayList<>();
        for (Map.Entry<String, FieldDescribe> entry : result.getData().getDescribe().getFields().entrySet()) {
            FieldDescribe fieldDescribe = entry.getValue();
            // 过滤系统字段
            if (crmObjectApiNameEnum == CrmObjectApiNameEnum.MEMBER){
                if("system".equals(fieldDescribe.getDefineType()) && !"name".equals(fieldDescribe.getApiName())){
                    continue;
                }
                ImmutableSet<String> excludeFields = ImmutableSet.of("lock_rule", "extend_obj_data_id", "life_status_before_invalid", "owner_department", "integral_value", "area_location", "lock_status",
                    "growth_value", "relevant_team", "lock_user", "grade_id", "life_status", "record_type", "customer_id", "location");
                if (excludeFields.contains(fieldDescribe.getApiName())){
                    continue;
                }
            }
            // 去除作废字段
            if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
                continue;
            }
            // 过滤无法处理字段
            if (StringUtils.isNotBlank(fieldDescribe.getApiName()) && CrmV2FieldTypeEnum.IllegalApiName(fieldDescribe.getApiName())) {
                continue;
            }
            // 特殊过滤
            ImmutableSet<String> excludeFields = ImmutableSet.of("created_by", "lock_user", "last_modified_by");
            if (excludeFields.contains(fieldDescribe.getApiName())){
                continue;
            }
            if (Objects.equals(CrmV2FieldTypeEnum.Auto_Number.getName(),fieldDescribe.getType())) {
                continue;
            }
            CrmUserDefineFieldVo crmUserDefineFieldVo = fieldDescribeToDefineVo(fieldDescribe);
            crmUserDefineFieldVo.setFieldTypeName(fieldDescribe.getType());
            crmUserDefineFieldVoList.add(crmUserDefineFieldVo);
        }

        return crmUserDefineFieldVoList;
    }

    private CrmUserDefineFieldVo fieldDescribeToDefineVo(FieldDescribe fieldDescribe) {
        CrmUserDefineFieldVo crmUserDefineFieldVo = new CrmUserDefineFieldVo();
        crmUserDefineFieldVo.setFieldName(fieldDescribe.getApiName());
        crmUserDefineFieldVo.setFieldCaption(fieldDescribe.getLabel());
        crmUserDefineFieldVo.setIsNotNull(fieldDescribe.isRequired() != null ? fieldDescribe.isRequired() : false);

        String defineType = (String) fieldDescribe.get("define_type");
        crmUserDefineFieldVo.setDefineType(defineType);
        if (StringUtils.isNotBlank(defineType) && defineType.equals("custom")) {
            crmUserDefineFieldVo.setFieldProperty(2);
        } else {
            crmUserDefineFieldVo.setFieldProperty(1);
        }
        //处理查找关联字段,返回其targetApiName;
        if (StringUtils.isNotBlank(fieldDescribe.getTargetApiName())) {
            crmUserDefineFieldVo.setTargetApiName(fieldDescribe.getTargetApiName());
        }

        List<Map<String, Object>> optionsList = (List<Map<String, Object>>) fieldDescribe.get("options");
        List<EnumDetailResult> enumDetails = getObjectFieldDescribesEnumDetails(optionsList);

        crmUserDefineFieldVo.setEnumDetails(enumDetails);
        crmUserDefineFieldVo.setFieldType(CrmV2FieldTypeEnum.getNumber(fieldDescribe.getType()));
        crmUserDefineFieldVo.setFieldTypeName(fieldDescribe.getType());
        return crmUserDefineFieldVo;
    }

    public List<CrmUserDefineFieldVo> getAllObjectFieldDescribesList(String ea, CrmObjectApiNameEnum crmObjectApiNameEnum) {
        return getAllObjectFieldDescribesList(ea, crmObjectApiNameEnum.getName());
    }

    public List<CrmUserDefineFieldVo> getAllObjectFieldDescribesList(String ea, String crmObjectApiName) {
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = new ArrayList<>();
        try {
            Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null), crmObjectApiName);
            for (Map.Entry<String, FieldDescribe> entry : result.getData().getDescribe().getFields().entrySet()) {
                FieldDescribe fieldDescribe = entry.getValue();
                CrmUserDefineFieldVo crmUserDefineFieldVo = fieldDescribeToDefineVo(fieldDescribe);
                crmUserDefineFieldVoList.add(crmUserDefineFieldVo);
            }
        } catch (Exception e) {
            log.warn("CrmV2Manager getAllObjectFieldDescribesList error", e);
        }
        return crmUserDefineFieldVoList;
    }

    public List<CrmUserDefineFieldVo> getAllObjectFieldDescribesList(String ea, ControllerGetDescribeResult fieldDescribeResullt) {
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = new ArrayList<>();
        try {
            for (Map.Entry<String, FieldDescribe> entry : fieldDescribeResullt.getDescribe().getFields().entrySet()) {
                FieldDescribe fieldDescribe = entry.getValue();
                CrmUserDefineFieldVo crmUserDefineFieldVo = fieldDescribeToDefineVo(fieldDescribe);
                crmUserDefineFieldVoList.add(crmUserDefineFieldVo);
            }
        } catch (Exception e) {
            log.warn("CrmV2Manager getAllObjectFieldDescribesList error", e);
        }
        return crmUserDefineFieldVoList;
    }

    private List<EnumDetailResult> getObjectFieldDescribesEnumDetails(List<Map<String, Object>> optionsList) {
        List<EnumDetailResult> enumDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(optionsList)) {
            for (Map<String, Object> map : optionsList) {
                EnumDetailResult enumDetailResult = new EnumDetailResult();
                if (null != map.get("not_usable") && "true".equals(map.get("not_usable").toString())) {
                    continue;
                }
                if (null != map.get("child_options")) {
                    List<Map<String, Object>> childOptionsList = (List<Map<String, Object>>) map.get("child_options");
                    List<EnumDetailResult> childEnumDetails = getObjectFieldDescribesEnumDetails(childOptionsList);
                    enumDetailResult.setChildren(childEnumDetails);
                }

                String value = String.valueOf(map.get("value"));
                if (StringUtils.isNotBlank(value)) {
                    enumDetailResult.setItemCode(value);
                }
                String label = String.valueOf(map.get("label"));
                if (StringUtils.isNotBlank(label)) {
                    enumDetailResult.setItemName(label);
                }
                if (map.get("is_required") != null) {
                    enumDetailResult.setIsRequired((Boolean) map.get("is_required"));
                }

                enumDetails.add(enumDetailResult);
            }
        }
        return enumDetails;
    }

    public Map<String, String> getObjectNameMapByIdsByCache(String ea, Integer fsUserId, String apiName, Set<String> ids){
        if (ids == null || ids.isEmpty()){
            return new HashMap<>(0);
        }
        Map<String, String> cacheKeyToObjectIdMap = ids.stream().collect(Collectors.toMap(id -> ea + ":" + apiName +  ":" + id, id -> id));

        ImmutableMap<String, String> cachedObjectKeyToNameMap = OBJECT_ID_TO_NAME_CACHE.getAllPresent(cacheKeyToObjectIdMap.keySet());
        Map<String, String> cachedObjectIdToNameMap = new HashMap<>(ids.size());
        for (Map.Entry<String, String> entry : cachedObjectKeyToNameMap.entrySet()) {
            cachedObjectIdToNameMap.put(cacheKeyToObjectIdMap.get(entry.getKey()), entry.getValue());
        }

        Set<String> idsNotCached = new HashSet<>(ids);
        idsNotCached.removeAll(cachedObjectIdToNameMap.keySet());
        if (!idsNotCached.isEmpty()){
            Map<String, String> notCachedObjectIdToNameMap = getObjectNameMapByIds(ea, fsUserId, apiName, idsNotCached, null);
            for (Map.Entry<String, String> entry : notCachedObjectIdToNameMap.entrySet()) {
                cachedObjectIdToNameMap.put(entry.getKey(), entry.getValue());
                OBJECT_ID_TO_NAME_CACHE.put(ea + ":" + apiName +  ":" + entry.getKey(), entry.getValue());
            }
        }
        return cachedObjectIdToNameMap;
    }
    
    public int countByFilters(String ea, Integer fsUserId, String apiName, List<Filter> filterList){
        Preconditions.checkArgument(filterList != null && !filterList.isEmpty());
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(apiName);
        controllerListArg.setIncludeLayout(false);
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setIncludeButtonInfo(false);
        SearchQuery searchQuery = new SearchQuery();
        filterList.forEach(data -> {
            searchQuery.addFilter(data.getFieldName(), data.getFieldValues(), data.getOperator());
        });
        searchQuery.setOffset(0);
        searchQuery.setLimit(1);
        if (StringUtils.isBlank(searchQuery.getSearchSource())) {
            searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
        }
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> result = metadataControllerService.list(createHeaderObj(ea, fsUserId), apiName, controllerListArg);
        if (!result.isSuccess() || result.getData() == null || result.getData().getDataList() == null){
            return 0;
        }
        return result.getData().getTotal();
    }

    public Map<String, String> getObjectNameMapByIds(String ea, Integer fsUserId, String apiName, Set<String> ids, List<Filter> filterList){
        if (ids == null || ids.isEmpty()){
            return new HashMap<>(0);
        }
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(apiName);
        controllerListArg.setIncludeLayout(false);
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setIncludeButtonInfo(false);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("_id", new ArrayList<>(ids), FilterOperatorEnum.IN);
        if (CollectionUtils.isNotEmpty(filterList)) {
            filterList.forEach(data -> {
                searchQuery.addFilter(data.getFieldName(), data.getFieldValues(), data.getOperator());
            });
        }
        searchQuery.setOffset(0);
        searchQuery.setLimit(ids.size());
        if (StringUtils.isBlank(searchQuery.getSearchSource())) {
            searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
        }
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> result = metadataControllerService.list(createHeaderObj(ea, fsUserId), apiName, controllerListArg);
        if (!result.isSuccess() || result.getData() == null || result.getData().getDataList() == null){
            return new HashMap<>(0);
        }
        return result.getData().getDataList().stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName, (v1, v2) -> v1));
    }
    
    public Map<String, ObjectData> getObjectDataMapByIds(String ea, Integer fsUserId, String apiName, Set<String> ids, List<String> projectionFieldNames){
        if (ids == null || ids.isEmpty()){
            return new HashMap<>(0);
        }
        if(projectionFieldNames == null){
            projectionFieldNames = new LinkedList<String>();
        }
        List<String> finalProjectionFieldNames = new LinkedList<>(projectionFieldNames);
        finalProjectionFieldNames.add("_id");
        
        Map<String, ObjectData> dataMap = new HashMap<>(ids.size());
        Iterators.partition(ids.iterator(), 200).forEachRemaining(partIds -> {
            ControllerListArg controllerListArg = new ControllerListArg();
            controllerListArg.setObjectDescribeApiName(apiName);
            controllerListArg.setIncludeLayout(false);
            controllerListArg.setIncludeDescribe(false);
            controllerListArg.setIncludeButtonInfo(false);
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.addFilter("_id", new ArrayList<>(partIds), FilterOperatorEnum.IN);
            searchQuery.setOffset(0);
            searchQuery.setLimit(partIds.size());
            if (StringUtils.isBlank(searchQuery.getSearchSource())) {
                searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
            }
            controllerListArg.setSearchQuery(searchQuery);
            controllerListArg.setFieldProjection(finalProjectionFieldNames);
            Result<Page<ObjectData>> result = metadataControllerService.list(createHeaderObj(ea, fsUserId), apiName, controllerListArg);
            if (result.getData() != null && result.getData().getDataList() != null){
                result.getData().getDataList().forEach(objectData -> {
                    dataMap.put(objectData.getId(), objectData);
                });
            }
        });
        return dataMap;
    }

    public List<ObjectData> listByEaAndPhone(String ea, Integer fsUserId, String objectApiName, String phone){
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("phone", Lists.newArrayList(phone), FilterOperatorEnum.EQ);
        searchQuery.setOffset(0);
        searchQuery.setLimit(20);
        Page<ObjectData> page = getList(ea, fsUserId, objectApiName, searchQuery);
        return page.getDataList() == null ? new ArrayList<>(0) : page.getDataList();
    }
    
    public Optional<ObjectData> getCrmLeadByPhone(String ea, Integer fsUserId, String phone){
        ObjectData mobileSearchResult = doGetObjectByPhone(ea, fsUserId, CrmObjectApiNameEnum.CRM_LEAD.getName(), phone, "mobile");
        if (mobileSearchResult != null){
            return Optional.of(mobileSearchResult);
        }
        return Optional.ofNullable(doGetObjectByPhone(ea, fsUserId, CrmObjectApiNameEnum.CRM_LEAD.getName(), phone, "tel"));
    }

    public ObjectData getDetail(String ea, Integer userId, String apiName, String id) {
        try {
            if (StringUtils.equals(CrmObjectApiNameEnum.PERSONNEL_OBJ.getName(), apiName) && StringUtils.isNumeric(id)) {
                // 员工对象特殊处理,如果id是数字,例如：1000，用userid作为过滤条件进行查询，之前出现过userid和对象数据id不一致的情况
                HashMap<String, String> paramMap = new HashMap<>();
                paramMap.put("user_id", id);
                return queryObjectData(ea, "PersonnelObj", paramMap);
            }
            ControllerDetailArg arg = new ControllerDetailArg();
            arg.setObjectDataId(id);
            arg.setObjectDescribeApiName(apiName);
            arg.setIncludeLayout(false);
            arg.setIncludeDescribe(false);
            return metadataControllerService.detail(createHeaderObj(ea, userId), apiName, arg).getData().getData();
        } catch (Exception e) {
            log.warn("CrmV2Manager.getDetail error, ea:{}, apiName:{}, objectId:{} ", ea, apiName, id, e);
            throw e;
        }
    }

    public ObjectData getDetailIgnoreError(String ea, Integer userId, String apiName, String id) {
        try {
            ControllerDetailArg arg = new ControllerDetailArg();
            arg.setObjectDataId(id);
            arg.setObjectDescribeApiName(apiName);
            arg.setIncludeLayout(false);
            arg.setIncludeDescribe(false);
            Result<ControllerGetDescribeResult> result = metadataControllerService.detail(createHeaderObj(ea, userId), apiName, arg);
            if (!result.isSuccess() || result.getData() == null || result.getData().getData() == null) {
                return null;
            }
            return result.getData().getData();
        } catch (Exception e) {
            log.warn("CrmV2Manager.getDetailIgnoreError error, ea:{}, apiName:{}, objectId:{} ", ea, apiName, id, e);
            return null;
        }
    }

    public ObjectData getOneByList(String ea, Integer userId, String apiName, String id) {
        Filter marketingEventIdFilter = new Filter();
        marketingEventIdFilter.setFieldName(CampaignMembersObjApiNameEnum.ID.getApiName());
        marketingEventIdFilter.setOperator(Filter.OperatorContants.IN);
        marketingEventIdFilter.setFieldValues(Lists.newArrayList(id));

        List<Filter> allFilterList = Lists.newArrayList();
        allFilterList.add(marketingEventIdFilter);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(1);
        searchQuery.setFilters(allFilterList);
        searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setSearchQuery(searchQuery);

        try {
            Result<Page<ObjectData>> objectResult = metadataControllerService.list(createHeaderObj(ea, userId), apiName, controllerListArg);
            if (!objectResult.isSuccess() || objectResult.getData() == null || CollectionUtils.isEmpty(objectResult.getData().getDataList())) {
                return null;
            }
            return objectResult.getData().getDataList().get(0);
        } catch (Exception e) {
            log.warn("CrmV2Manager.getDatailByObjId error e:{}", e);
            return null;
        }
    }

    public Map<String, String> getObjectDataEnTextVal(String ea, Integer userId, String apiName, String id) {
        Map<String, Map<String, String>> objectDataTextVal = this.getObjectDataTextVal(ea, userId, apiName, id, false);
        return objectDataTextVal.get("en");
    }

    public Map<String, String> getObjectDataEnTextValWithNull(String ea, Integer userId, String apiName, String id) {
        Map<String, Map<String, String>> objectDataTextVal = this.getObjectDataTextVal(ea, userId, apiName, id, true);
        return objectDataTextVal.get("en");
    }

    /**
     * 获取经过转换后的文本类型对象数据(暂支持部分)
     * 字段类型：1、分割线；2、单行文本；3、多行文本；4、整数；5、小数；6、金额；7、日期时间；
     *          8、单选；9、多选；10、图像；11、地址；12、生日；13、布尔型；14、级联单选；
     *          15、日期；16、单选选人控件;17、附件类型；18、电话；19、邮件
     * @param ea
     * @param userId
     * @param apiName
     * @param id
     * @return
     */
    public Map<String, Map<String, String>> getObjectDataTextVal(String ea, Integer userId, String apiName, String id, boolean whitNull) {
        List<CrmUserDefineFieldVo> allObjectFieldDescribesList = this.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.fromName(apiName));
        ObjectData objectData = this.getOneByList(ea, userId, apiName, id);
        return this.objectDataTransaction(ea, userId, allObjectFieldDescribesList, objectData, whitNull);
    }

    public Map<String, Map<String, String>> objectDataTransaction(String ea, Integer userId, List<CrmUserDefineFieldVo> allObjectFieldDescribesList, ObjectData objectData, boolean whitNull) {
        Map<String, Map<String, String>> result = new HashMap<>();
        Map<String, String> enResult = new HashMap<>();
        Map<String, String> zhResult = new HashMap<>();
        result.put("en", enResult);
        result.put("zh", zhResult);
        if (!allObjectFieldDescribesList.isEmpty() && objectData != null) {
            allObjectFieldDescribesList.forEach(objectFieldDescribes->{
                String key = objectFieldDescribes.getFieldName();
                String zhKey = objectFieldDescribes.getFieldCaption();
                Object val = objectData.get(key);
                String value = "";
                if (!whitNull) {
                    value = I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996);
                }
                if (val != null) {
                    if (objectFieldDescribes.getFieldType() != null) {
                        switch (objectFieldDescribes.getFieldType()) {
                            case 7:
                                value = new SimpleDateFormat("yyyy-MM-dd").format(BigDecimal.valueOf((Double) val).longValue());
                                break;
                            case 8:
                                List<EnumDetailResult> enumDetails = objectFieldDescribes.getEnumDetails();
                                for (EnumDetailResult item : enumDetails) {
                                    if (item.getItemCode().equals(String.valueOf(val))) {
                                        value = item.getItemName();
                                    }
                                }
                                break;
                            case 9:
                                List<String> valueList = new ArrayList<>();
                                if (val instanceof List) {
                                    List valList = (List) val;
                                    for (EnumDetailResult item : objectFieldDescribes.getEnumDetails()) {
                                        if (valList.contains(item.getItemCode())) {
                                            if ("other".equals(item.getItemCode())) {
                                                valueList.add(item.getItemName() + ":" + objectData.get(key + "__o"));
                                                continue;
                                            }
                                            valueList.add(item.getItemName());
                                        }
                                    }
                                    value = String.join(",", valueList);
                                }
                                break;
                            case 13:
                                value = "true".equals(String.valueOf(val)) ? "是" : "否";
                                break;
                            case 15:
                                value = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(BigDecimal.valueOf((Double) val).longValue());
                                break;
                            case 16:
                                value = String.valueOf(objectData.get(key + "__r"));
                                break;
                            case 31:
                            case 32:
                                if (val instanceof List) {
                                    List<String> valList = (List) val;
                                    Map<Integer, FsAddressBookManager.FSEmployeeMsg> employeeInfoByUserIds = fsAddressBookManager.getEmployeeInfoByUserIds(ea,
                                            valList.stream().map(Integer::valueOf).collect(Collectors.toList()), false);
                                    if (employeeInfoByUserIds != null) {
                                        value = String.join(",", employeeInfoByUserIds.values().stream().map(FsAddressBookManager.FSEmployeeMsg::getName).collect(Collectors.toList()));
                                    }
                                }
                                break;
                            case 33:
                                value = new SimpleDateFormat("HH:mm").format(BigDecimal.valueOf((Double) val).longValue());
                                break;
                            default:
                                value = String.valueOf(val);
                        }
                    } else {
                        value = String.valueOf(val);
                    }
                }
                enResult.put(key, value);
                zhResult.put(zhKey, value);
            });
        }
        return result;
    }

    public ObjectData getObjectData(String ea, Integer userId, String apiName, String id) {
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDataId(id);
        arg.setObjectDescribeApiName(apiName);
        arg.setIncludeLayout(false);
        arg.setIncludeDescribe(false);
        Result<ControllerGetDescribeResult> resultResult = metadataControllerService.detail(createHeaderObj(ea, userId), apiName, arg);
        if (resultResult.getCode() == 0 && resultResult.getData() != null) {
            return resultResult.getData().getData();
        }
        return null;
    }

    private ObjectData doGetObjectByPhone(String ea, Integer fsUserId, String objectApiName, String phone, String phoneField) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(phoneField, Lists.newArrayList(phone), FilterOperatorEnum.EQ);
        searchQuery.setOffset(0);
        searchQuery.setLimit(20);
        Page<ObjectData> page = getList(ea, fsUserId, objectApiName, searchQuery);
        return (page.getDataList() == null || page.getDataList().isEmpty()) ? null : page.getDataList().get(0);
    }

    public Result<ControllerGetDescribeResult> getDetailWithMessage(String ea, Integer userId, String apiName, String id){
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDataId(id);
        arg.setObjectDescribeApiName(apiName);
        arg.setIncludeLayout(false);
        arg.setIncludeDescribe(false);
        return metadataControllerService.detail(createHeaderObj(ea, userId), apiName, arg);
    }

    public ObjectData getDetailByEi(Integer tenantId, Integer userId, String apiName, String id) {
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDataId(id);
        arg.setObjectDescribeApiName(apiName);
        arg.setIncludeLayout(false);
        arg.setIncludeDescribe(false);
        return metadataControllerService.detail(new HeaderObj(tenantId, userId), apiName, arg).getData().getData();
    }

    public List<Map<String, Object>> queryDuplicateV2(String ea, String apiName, Map<String, Object> params) {
        // 先调用普通查重接口，再调用联合查询接口
        DuplicateSearchResult duplicateSearchResult = duplicateSearchResult(ea, apiName, params, 1, 20);
        if (duplicateSearchResult == null) {
            log.warn("CrmV2Manager.queryDuplicateV2 error, ea: {} apiName: {} params: {}", ea, apiName, params);
            return Lists.newArrayList();
        }
        List<Map<String, Object>> duplicateDataList = CollectionUtils.isNotEmpty(duplicateSearchResult.getDataList()) ? duplicateSearchResult.getDataList() : Lists.newArrayList();
        for (Map<String, Object> objectMap : duplicateDataList) {
            log.info("duplicate data, total: {} apiName: {} objectName: {} objectId: {}", duplicateDataList.size(), objectMap.get("object_describe_api_name"), objectMap.get("name"), objectMap.get("_id"));
        }
        // 如果是新查重，那么联合查重接口必须带上该参数，否则就会返回空
        String duplicateRuleApiName = null;
        Map<String, Object>  map = duplicateSearchResult.getDuplicateRuleInfo();
        if (MapUtils.isNotEmpty(map) && map.containsKey("duplicate_rule_api_name")) {
            duplicateRuleApiName = (String) map.get("duplicate_rule_api_name");
        }
        RelatedDuplicateSearchResult relatedDuplicateSearchResult = relatedDuplicateSearch(ea, apiName, params, 1, 20, duplicateRuleApiName);
        if (relatedDuplicateSearchResult != null && CollectionUtils.isNotEmpty(relatedDuplicateSearchResult.getResults())) {
            List<Map<String, Object>> relatedDuplicateDataList = relatedDuplicateSearchResult.getResults().stream().map(RelatedDuplicateSearchResult.DataList::getDataList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
            for (Map<String, Object> objectMap : relatedDuplicateDataList) {
                duplicateDataList.add(objectMap);
                log.info("relatedDuplicate data, total: {} apiName: {} objectName: {} objectId: {}", relatedDuplicateDataList.size(), objectMap.get("object_describe_api_name"), objectMap.get("name"), objectMap.get("_id"));
            }
        }
        return duplicateDataList;
    }


    public DuplicatesearchQueryResult queryDuplicate(String ea, Integer userId, String apiName, Map<String, Object> params) {
        ObjectData data = new ObjectData();
        data.putAll(params);
        DuplicatesearchQueryArg arg = new DuplicatesearchQueryArg();
        arg.setMasterApiName(apiName);
        arg.setData(data);
        return duplicatesearchService.query(createHeaderObj(ea, userId), arg).getData();
    }

    //TODO 还有两个duplicateRuleApiName传null的 要处理
    public RelatedDuplicateSearchResult relatedDuplicateSearch(String ea, String apiName, Map<String, Object> param, Integer pageNum, Integer pageSize, String duplicateRuleApiName) {
        RelatedDuplicateSearchArg relatedDuplicateSearchArg = new RelatedDuplicateSearchArg();
        relatedDuplicateSearchArg.setDescribeApiName(apiName);
        relatedDuplicateSearchArg.setIsNeedDuplicate(true);
        relatedDuplicateSearchArg.setType("NEW");
        relatedDuplicateSearchArg.setObjectData(param);
        relatedDuplicateSearchArg.setPageSize(pageSize);
        relatedDuplicateSearchArg.setPageNum(pageNum);
        relatedDuplicateSearchArg.setDuplicateRuleApiName(duplicateRuleApiName);
        return duplicatesearchService.relatedDuplicateSearch(createHeaderObj(ea, null), apiName, relatedDuplicateSearchArg).getData();
    }



    @FilterLog
    public DuplicateSearchResult duplicateSearchResult(String ea, String apiName, Map<String, Object> param, Integer pageNum, Integer pageSize) {
        DuplicateSearchArg arg = new DuplicateSearchArg();
        arg.setDescribeApiName(apiName);
        arg.setType("NEW");
        arg.setObjectData(param);
        arg.setIncludeObjectDescribes(false);
        arg.setIsNeedDuplicate(true);
        arg.setPageSize(pageSize);
        arg.setPageNum(pageNum);
        return duplicatesearchService.duplicateSearch(createHeaderObj(ea, null), apiName, arg).getData();
    }

    public ObjectDataQueryListByIdsResult batchQueryObjectInfo(String ea, String apiName, List<String> ids) {
        return objectDataService.queryListByIds(createHeaderObj(ea, null), apiName, ids).getData();
    }

    public CreateLeadResult createLead(String ea, Integer userId, Map<String, Object> params, boolean needSetOwner, Boolean isDuplicateSearch) {
        ContactData data = new ContactData();
        data.putAll(params);
        data.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CRM_LEAD.getName());
        data.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CRM_LEAD.getName());
        data.put("ObjectCreateFrom", "marketing");

        if (needSetOwner) {
            List<String> ownerIds = Arrays.asList(userId.toString());
            data.put(CrmV2LeadFieldEnum.OwnerID.getNewFieldName(), ownerIds);
            // 不传线索池id能保证OwnerID设置有效
            data.remove(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName());
        }

        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
        optionInfo.setCalculateDefaultValue(true);
        if (isDuplicateSearch != null) {
            optionInfo.setIsDuplicateSearch(isDuplicateSearch);
        }
        arg.setOptionInfo(optionInfo);

        CreateLeadResult createLeadResult = new CreateLeadResult();
        Result<ActionAddResult> result = null;
        HeaderObj headerObj = createHeaderObj(ea, userId);
        headerObj.put("x-fs-peer-name","fs-marketing");
        try {

            result = metadataActionService.add(headerObj, LeadsFieldContants.API_NAME, false, arg);
            // 离职员工创建的微页面表单存入时使用系统身份创建
            if (320001400 == result.getCode()) {
                headerObj = createHeaderObj(ea, null);
                result = metadataActionService.add(headerObj, LeadsFieldContants.API_NAME, false, arg);
            }
            if (result != null && result.getCode() == Result.SUCCESS_CODE) {
                createLeadResult.setLeadId((String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName()));
                createLeadResult.setMessage(result.getMessage());
            } else if (result != null && result.getCode() == CrmConstants.REPEAT_CODE) {
                createLeadResult.setCode(CrmConstants.REPEAT_CODE);
                createLeadResult.setMessage(I18nUtil.get(I18nKeyStaticEnum.MARK_STATIC_CRMSTATUSMESSAGECONSTANT_LEAD_REPETITION));
            } else {
                createLeadResult.setMessage(result != null ? result.getMessage() : I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
            }
        } catch (Exception e) {
            log.warn("CrmV2Manager.createLead error e", e);
            createLeadResult.setMessage(e.getMessage());
        }

        return createLeadResult;
    }

    public CreateLeadResult updateLead(String leadId, String ea, Integer userId, Map<String, Object> params) {
        ContactData data = new ContactData();
        data.putAll(params);
        data.put(CrmV2LeadFieldEnum.ID.getNewFieldName(), leadId);
        CreateLeadResult createLeadResult = new CreateLeadResult();
        Result<ActionEditResult> result = null;
        HeaderObj headerObj = createHeaderObj(ea, userId);
        headerObj.put("x-fs-peer-name","fs-marketing");
        try {
            ActionEditArg arg = new ActionEditArg();
            arg.setObjectData(data);
            result = metadataActionService.edit(headerObj, LeadsFieldContants.API_NAME, true, true, arg);
            if (result != null && result.getCode() == Result.SUCCESS_CODE) {
                createLeadResult.setLeadId((String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName()));
                createLeadResult.setMessage(result.getMessage());
            } else {
                createLeadResult.setMessage(result != null ? result.getMessage() : I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
            }
        } catch (Exception e) {
            log.warn("CrmV2Manager.updateLead error e", e);
            createLeadResult.setMessage(e.getMessage());
        }
        return createLeadResult;
    }

    public boolean incrementUpdate(String ea, String apiName, Map<String, Object> params) {
        if (StringUtils.isBlank(ea) || MapUtils.isEmpty(params)) {
            return false;
        }
        IncrementUpdateArg incrementUpdateArg = new IncrementUpdateArg();
        ObjectData objectData = new ObjectData();
        objectData.putAll(params);
        incrementUpdateArg.setData(objectData);
        try {
            Result<IncrementUpdateResult> incrementUpdateResult = metadataActionService.incrementUpdate(createHeaderObj(ea, -10000), apiName, incrementUpdateArg);
            if (incrementUpdateResult == null || incrementUpdateResult.getData() == null) {
                return false;
            }
        } catch (Exception e) {
            log.warn("CrmV2Manager.incrementUpdate error e:{}", e);
            return false;
        }
        return true;
    }

    public Map<String, Boolean> funcPermissionCheck(String ea, Integer userId, List<String> funcCode) {
        Integer ei = eIEAConverter.enterpriseAccountToId(ea);
        FuncPermissionCheckArg arg = new FuncPermissionCheckArg("CRM", ei, userId);
        arg.setFuncCodeList(funcCode);
        return paasAuthService.funcPermissionCheck(com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(ei), arg).getResult();
    }

    /**
     * 校验某人是否有操作某项数据权限
     * @return
     */
    public boolean checkPrivilege(String ea, Integer userId, String dataId, String apiName, String funcCode) {
        CheckPrivilegeArg checkPrivilegeArg = new CheckPrivilegeArg();
        checkPrivilegeArg.setId(dataId);
        checkPrivilegeArg.setObjectDescribeApiName(apiName);
        checkPrivilegeArg.setActionCode(funcCode);
        InnerResult<CheckPrivilegeResult> innerResult = objectService.checkPrivilege(createHeaderObj(ea, userId), checkPrivilegeArg);
        if (!innerResult.isSuccess()) {
            log.warn("CrmV2Manager.checkPrivilege error innerResult:{}", innerResult);
            return false;
        }
        return !innerResult.getResult().getValue().equals(CheckPrivilegeEnum.NO_PERMISSION.getCode());
    }

    public List<GetLeadsPoolListByEmployeeIdResult.LeadsPoolData> getLeadPoolsList(String ea, Integer userId) {
        GetLeadsPoolListByEmployeeIdArg getLeadsPoolListByEmployeeIdArg = new GetLeadsPoolListByEmployeeIdArg();
        getLeadsPoolListByEmployeeIdArg.setEmployeeId(userId);
        return leadsPoolService.getLeadsPoolListByEmployeeId(createHeaderObj(ea, userId), getLeadsPoolListByEmployeeIdArg).getData().getValue();
    }

    public List<ObjectRecord> getRecordTypeList(String ea, String apiName) {
        ObjectRecordTypeFindRecordTypeListArg arg = new ObjectRecordTypeFindRecordTypeListArg();
        arg.setDescribeApiName(apiName);
        InnerResult<ObjectRecordTypeFindRecordTypeListResult> resultInnerResult = objectRecordTypeService.findRecordTypeList(createHeaderObj(ea, null), arg);
        if (!resultInnerResult.isSuccess()) {
            log.warn("CrmV2Manager.getRecordTypeList error ea:{},apiName:{},resultInnerResult:{}", ea, apiName,resultInnerResult);
            return Lists.newArrayList();
        }
        return objectRecordTypeService.findRecordTypeList(createHeaderObj(ea, null), arg).getResult().getRecordList();
    }

    public Page<ObjectData> getList(String ea, Integer userId, String apiName, SearchQuery searchQuery) {
        try {
            ControllerListArg arg = new ControllerListArg();
            arg.setObjectDescribeApiName(apiName);
            if (StringUtils.isBlank(searchQuery.getSearchSource())) {
                searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
            }
            arg.setSearchQuery(searchQuery);
            arg.setIncludeLayout(false);
            arg.setIncludeDescribe(false);
//            arg.setIncludeButtonInfo(false); 注释掉,参数字段似乎被修改了,否则接口那边报错
            return metadataControllerService.list(createHeaderObj(ea, userId), apiName, arg).getData();
        } catch (Exception e) {
            log.info("CrmV2Manager getList exception, ea : {} , userId : {} , apiName:{},searchQuery={}", ea, userId, apiName, searchQuery);
            throw new RuntimeException(e);
        }
    }
    
    public Page<ObjectData> getList(String ea, Integer userId, String apiName, SearchQuery searchQuery, List<String> fieldsProjection) {
        try {
            ControllerListArg arg = new ControllerListArg();
            arg.setObjectDescribeApiName(apiName);
            if (StringUtils.isBlank(searchQuery.getSearchSource())) {
                searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
            }
            arg.setSearchQuery(searchQuery);
            arg.setFieldProjection(fieldsProjection);
            arg.setIncludeLayout(false);
            arg.setIncludeDescribe(false);
            arg.setIncludeButtonInfo(false);
            return metadataControllerService.list(createHeaderObj(ea, userId), apiName, arg).getData();
        } catch (Exception e) {
            log.info("CrmV2Manager getList exception, ea : {} , userId : {} , apiName:{},searchQuery={}", ea, userId, apiName, searchQuery);
            throw new RuntimeException(e);
        }
    }

    public int getCrmObjectEntityTotalCount(String ea, String aipName, List<String> marketingEventIds, List<String> timeList){
        ControllerListArg params = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        searchQuery.addFilter("marketing_event_id", marketingEventIds, FilterOperatorEnum.IN);
        if (CollectionUtils.isNotEmpty(timeList)) {
            searchQuery.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), timeList, OperatorConstants.BETWEEN);
        }
        searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
        params.setSearchQuery(searchQuery);
        params.setObjectDescribeApiName(aipName);
        Integer total = metadataControllerServiceManager.getTotal(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), aipName, params);
        return total == null ? 0 : total.intValue();
    }

    public boolean isOpenCrm(String ea) {
        BaseResult<Integer> baseResult = quotaService.queryQuota(ea, crmAppId);
        if (!baseResult.isSuccess()) {
            throw new IllegalStateException("ea=" + ea + ",errorCode=" + baseResult.getErrCode() + ",msg=" + baseResult.getErrMessage());
        }
        if (baseResult.getResult() == null) {
            return false;
        }
        return baseResult.getResult() > 0;
    }

    public HeaderObj createHeaderObj(String ea, Integer userId) {
        Integer tenantId = eIEAConverter.enterpriseAccountToId(ea);
        if (null == tenantId) {
            throw new CrmBusinessException(-1000, "enterpriseAccountToId failed, ea=" + ea);
        }

        if (null == userId) {
            userId = -10000;
        }

        return new HeaderObj(tenantId, userId);
    }

    private boolean objectFieldDescribesFilter(CrmObjectApiNameEnum crmObjectApiNameEnum, FieldDescribe fieldDescribe) {
        String apiName = fieldDescribe.getApiName();
        if (StringUtils.isBlank(apiName)) {
            return false;
        }

        String defineType = (String) fieldDescribe.get("define_type");
        if (StringUtils.isNotBlank(defineType) && defineType.equals("custom")) {
            return false;
        }

        if (apiName.startsWith("UD")) {
            return false;
        }

        // 去除作废字段
        if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
            return true;
        }

        if (crmObjectApiNameEnum == CrmObjectApiNameEnum.CRM_LEAD) {
            Boolean needFilter = CrmV2LeadFieldEnum.needFilter(apiName);
            if (null == needFilter) {
                //返回查找关联字段
                if (Objects.equals(CrmV2FieldTypeEnum.ObjectReference.getName(),fieldDescribe.getType())) {
                    return false;
                }
                return true;
            }
            if (needFilter) {
                return true;
            }
        } else if (crmObjectApiNameEnum == CrmObjectApiNameEnum.CUSTOMER) {
            Boolean needFilter = CrmV2CustomerFieldEnum.needFilter(apiName);
            if (null == needFilter) {
                //返回查找关联字段
                if (Objects.equals(CrmV2FieldTypeEnum.ObjectReference.getName(),fieldDescribe.getType())) {
                    return false;
                }
                return true;
            }
            if (needFilter) {
                return true;
            }
        } else if (crmObjectApiNameEnum == CrmObjectApiNameEnum.CONTACT) {
            Boolean needFilter = CrmV2ContactFieldEnum.needFilter(apiName);
            if (null == needFilter) {
                //返回查找关联字段
                if (Objects.equals(CrmV2FieldTypeEnum.ObjectReference.getName(),fieldDescribe.getType())) {
                    return false;
                }
                return true;
            }
            if (needFilter) {
                return true;
            }
        }else if (crmObjectApiNameEnum == CrmObjectApiNameEnum.MARKETING_EVENT){
            Boolean needFilter = CrmV2MarketingEventFilterEnum.needFilter(apiName);
            if (needFilter != null && needFilter) {
                return true;
            }
        } else {
            Boolean needFilter = CrmV2ObjectFilterEnum.needFilter(apiName);
            if (needFilter != null && needFilter) {
                return true;
            }
        }
        return false;
    }

    private boolean objectFieldDescribesFilterByObjectApiName(String objectApiName, FieldDescribe fieldDescribe) {
        String apiName = fieldDescribe.getApiName();
        if (StringUtils.isBlank(apiName)) {
            return false;
        }

        String defineType = (String) fieldDescribe.get("define_type");
        if (StringUtils.isNotBlank(defineType) && defineType.equals("custom")) {
            return false;
        }

        if (apiName.startsWith("UD")) {
            return false;
        }

        // 去除作废字段
        if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
            return true;
        }

        if (objectApiName.equals(CrmObjectApiNameEnum.CRM_LEAD.getName())) {
            Boolean needFilter = CrmV2LeadFieldEnum.needFilter(apiName);
            if (null == needFilter || needFilter) {
                return true;
            }
        } else if (objectApiName.equals(CrmObjectApiNameEnum.CUSTOMER.getName())) {
            Boolean needFilter = CrmV2CustomerFieldEnum.needFilter(apiName);
            if (null == needFilter || needFilter) {
                return true;
            }
        } else if (objectApiName.equals(CrmObjectApiNameEnum.CONTACT.getName())) {
            Boolean needFilter = CrmV2ContactFieldEnum.needFilter(apiName);
            if (null == needFilter || needFilter) {
                return true;
            }
        }else if (objectApiName.equals(CrmObjectApiNameEnum.MARKETING_EVENT.getName())){
            Boolean needFilter = CrmV2MarketingEventFilterEnum.needFilter(apiName);
            if (needFilter != null && needFilter) {
                return true;
            }
        } else {
            Boolean needFilter = CrmV2ObjectFilterEnum.needFilter(apiName);
            if (needFilter != null && needFilter) {
                return true;
            }
        }
        return false;
    }

    /**
     * 这个性能很差，使用v2
     * @see CrmV2Manager#listCrmObjectByFilterV3
     */
    @Deprecated
    public InnerPage<ObjectData> listCrmObjectByFilter(String ea, Integer fsUserId, PaasQueryFilterArg paasQueryFilterArg, Integer maxCount) {
        maxCount = maxCount != null ? maxCount : 10000;
        paasQueryFilterArg.getQuery().addOrderByAsc(ObjectDescribeContants.ID, true);
        int limit = 100;
        int offset = 0;
        InnerPage<ObjectData> crmObjectFinalResult = new InnerPage();
        crmObjectFinalResult.setDataList(new ArrayList<>());
        for (int i = 0; i < 1000; i++) {
            try {
                paasQueryFilterArg.getQuery().setLimit(limit);
                paasQueryFilterArg.getQuery().setOffset(offset);
                ControllerQueryByFilterArg arg = BeanUtil.copyByGson(paasQueryFilterArg, ControllerQueryByFilterArg.class);
                InnerPage<ObjectData> pageResult = crmMetadataManager.list(ea, fsUserId, arg);
                if (pageResult != null && pageResult.getDataList() != null && !pageResult.getDataList().isEmpty()) {
                    crmObjectFinalResult.getDataList().addAll(pageResult.getDataList());
                    if (crmObjectFinalResult.getDataList().size() >= maxCount) {
                        log.warn("查询数据超过限制, arg:{}", arg);
                        break;
                    }
                }
                offset = offset + limit;
                pageResult = pageResult == null ? new InnerPage() : pageResult;
                int dataSize = pageResult.getDataList() == null ? 0 : pageResult.getDataList().size();
                log.info("query data , finished data  totalCount : {} , offset : {} , limit:{},data size={}", pageResult.getTotalCount(), offset, limit, dataSize);
                if (dataSize < limit) {
                    break;
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return crmObjectFinalResult;
    }


    public int countCrmObjectByFilterV3(String ea, Integer fsUserId, PaasQueryFilterArg paasQueryFilterArg) {
        PaasQueryArg query = paasQueryFilterArg.getQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource(PaasSearchSourceEnum.ES.getValue());
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setDescribeApiName(paasQueryFilterArg.getObjectAPIName());
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(query));
        findByQueryV3Arg.setSelectFields(paasQueryFilterArg.getSelectFields());
        findByQueryV3Arg.setPaginationOptimization(true);
        InnerPage<ObjectData> pageResult = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
        return pageResult.getTotalCount() == null ? 0 : pageResult.getTotalCount();
    }

    @FilterLog
    public InnerPage<ObjectData> listCrmObjectByFilterV3(String ea, Integer fsUserId, PaasQueryFilterArg paasQueryFilterArg, int page, int pageSize) {
        paasQueryFilterArg.getQuery().setLimit(pageSize);
        paasQueryFilterArg.getQuery().setOffset((page - 1) * pageSize);
        if (StringUtils.isBlank(paasQueryFilterArg.getQuery().getSearchSource())) {
            paasQueryFilterArg.getQuery().setSearchSource(PaasSearchSourceEnum.ES.getValue());
        }
        paasQueryFilterArg.getQuery().addOrderByAsc(ObjectDescribeContants.ID, true);
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setDescribeApiName(paasQueryFilterArg.getObjectAPIName());
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg.getQuery()));
        findByQueryV3Arg.setSelectFields(paasQueryFilterArg.getSelectFields());
        return crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
    }

    /**
     * @param lastId 上次的id，第一次为空
     */
    public InnerPage<ObjectData> listCrmObjectScanByIdV3(String ea, Integer fsUserId, PaasQueryFilterArg paasQueryFilterArg, String lastId, int pageSize) {

        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setDescribeApiName(paasQueryFilterArg.getObjectAPIName());
        findByQueryV3Arg.setSelectFields(paasQueryFilterArg.getSelectFields());
        findByQueryV3Arg.setPaginationOptimization(true);

        PaasQueryArg queryArg = paasQueryFilterArg.getQuery();

        PaasQueryArg finalQueryArg = JsonUtil.fromJson(JsonUtil.toJson(queryArg), PaasQueryArg.class);
        finalQueryArg.setLimit(pageSize);
        finalQueryArg.setOffset(0);
        finalQueryArg.addOrderByAsc(ObjectDescribeContants.ID, true);
        if (StringUtils.isNotBlank(lastId)) {
            finalQueryArg.addFilter("_id", PaasAndCrmOperatorEnum.GT.getCrmOperator(), Lists.newArrayList(lastId));
        }
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(finalQueryArg));

        InnerPage<ObjectData> innerPage = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
        if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
            innerPage = new InnerPage<>();
            innerPage.setDataList(Lists.newArrayList());
        }
        return innerPage;
    }

    public void listCrmObjectScanByIdAndHandle(String ea, Integer fsUserId, PaasQueryFilterArg paasQueryFilterArg, int limit, Consumer<ObjectData> handler) {
        List<ObjectData> dataList;
        String lastRelationId = null;
        do {
            dataList = null;
            InnerPage<ObjectData> objectDataInnerPage = null;
            int count = 3;
            while (count > 0) {
                try {
                    objectDataInnerPage = this.listCrmObjectScanByIdV3(ea, fsUserId, paasQueryFilterArg, lastRelationId, limit);
                } catch (Exception e) {
                    log.warn("crmV2Manager.listCrmObjectScanByIdAndHandle fail", e);
                }
                if (objectDataInnerPage != null) {
                    break;
                }
                count--;
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    Thread.interrupted();
                }
            }
            if (objectDataInnerPage != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                dataList = objectDataInnerPage.getDataList();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataList)) {
                    lastRelationId = dataList.get(dataList.size() - 1).getId();
                    try {
                        dataList.forEach(handler);
                    } catch (Exception e) {
                        log.warn("crmV2Manager.listCrmObjectScanByIdAndHandle handler fail", e);
                    }
                }
            }
        } while (dataList != null && dataList.size() == limit);
    }

    //该接口不要再使用，请用listCrmObjectScanByIdV3替代
    @Deprecated
    @FilterLog
    public InnerPage<ObjectData> listCrmObjectByFilterV3(String ea, Integer fsUserId, PaasQueryFilterArg paasQueryFilterArg, Integer maxCount) {
        maxCount = maxCount != null ? maxCount : 10000;
        paasQueryFilterArg.getQuery().addOrderByAsc(ObjectDescribeContants.ID, true);
        int limit = 100;
        int offset = 0;
        InnerPage<ObjectData> crmObjectFinalResult = new InnerPage();
        crmObjectFinalResult.setDataList(new ArrayList<>());
        for (int i = 0; i < 1000; i++) {
            try {
                paasQueryFilterArg.getQuery().setLimit(limit);
                paasQueryFilterArg.getQuery().setOffset(offset);
                FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
                findByQueryV3Arg.setDescribeApiName(paasQueryFilterArg.getObjectAPIName());
                if (StringUtils.isBlank(paasQueryFilterArg.getQuery().getSearchSource())) {
                    paasQueryFilterArg.getQuery().setSearchSource(PaasSearchSourceEnum.ES.getValue());
                }
                findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg.getQuery()));
                findByQueryV3Arg.setSelectFields(paasQueryFilterArg.getSelectFields());
                InnerPage<ObjectData> pageResult = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
                if (pageResult != null && pageResult.getDataList() != null && !pageResult.getDataList().isEmpty()) {
                    crmObjectFinalResult.getDataList().addAll(pageResult.getDataList());
                    if (crmObjectFinalResult.getDataList().size() >= maxCount) {
                        log.warn("查询数据超过限制, arg:{}", findByQueryV3Arg);
                        break;
                    }
                }
                offset = offset + limit;
                pageResult = pageResult == null ? new InnerPage() : pageResult;
                int dataSize = pageResult.getDataList() == null ? 0 : pageResult.getDataList().size();
                log.info("query data , finished data  totalCount : {} , offset : {} , limit:{},data size={}", pageResult.getTotalCount(), offset, limit, dataSize);
                if (dataSize < limit) {
                    break;
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return crmObjectFinalResult;
    }

    @FilterLog
    public InnerPage<ObjectData> concurrentListCrmObjectByFilterV3(String ea, Integer fsUserId, PaasQueryFilterArg paasQueryFilterArg) {
        paasQueryFilterArg.getQuery().addOrderByAsc(ObjectDescribeContants.ID, true);
        paasQueryFilterArg.getQuery().setLimit(1);
        paasQueryFilterArg.getQuery().setOffset(0);
        if (StringUtils.isBlank(paasQueryFilterArg.getQuery().getSearchSource())) {
            paasQueryFilterArg.getQuery().setSearchSource(PaasSearchSourceEnum.ES.getValue());
        }
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg.getQuery()));
        findByQueryV3Arg.setDescribeApiName(paasQueryFilterArg.getObjectAPIName());
        findByQueryV3Arg.setSelectFields(paasQueryFilterArg.getSelectFields());
        InnerPage<ObjectData> result = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
        Integer totalCount = result.getTotalCount();
        log.warn("concurrentListCrmObjectByFilterV3 totalCount:{}", totalCount);
        int limit = 1000;
        int offset = 0;
        int page = (totalCount - 1) / limit + 1;
        InnerPage<ObjectData> crmObjectFinalResult = new InnerPage<>();
        List<ObjectData> dataList = new CopyOnWriteArrayList<>();
        crmObjectFinalResult.setDataList(dataList);
        crmObjectFinalResult.setTotalCount(totalCount);
        CountDownLatch countDownLatch = new CountDownLatch(page);
        List<FindByQueryV3Arg> argList = new ArrayList<>();
        for (int i = 1; i <= page; i++) {
            paasQueryFilterArg.getQuery().setLimit(limit);
            paasQueryFilterArg.getQuery().setOffset(offset);
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg.getQuery()));
            findByQueryV3Arg.setNeedCount(false);
            argList.add(JSONObject.parseObject(JSON.toJSONString(findByQueryV3Arg), FindByQueryV3Arg.class));
            offset = offset + limit;
        }
        TraceContext context = TraceContext.get();
        for (FindByQueryV3Arg byQueryV3Arg : argList) {
            executorService.submit(() -> {
                try {
                    TraceContext._set(context);
                    long t1 = System.currentTimeMillis();
                    InnerPage<ObjectData> pageResult = crmMetadataManager.listV3(ea, fsUserId, byQueryV3Arg);
                    long t2 = System.currentTimeMillis();
                    boolean isSuccess = pageResult != null && CollectionUtils.isNotEmpty(pageResult.getDataList());
                    String printfLog = isSuccess ? getPrintfLog(pageResult) : " is not success ";
                    log.info("concurrentListCrmObjectByFilterV3 ea: {} cost: {} arg: {} result: {}", ea, t2 - t1, byQueryV3Arg, printfLog);
                    if (isSuccess) {
                        dataList.addAll(pageResult.getDataList());
                    }
                } catch (Exception e) {
                    log.warn("concurrentListCrmObjectByFilterV3 error arg:{}", JSON.toJSONString(findByQueryV3Arg));
                } finally {
                    TraceContext.remove();
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("concurrentListCrmObjectByFilterV3 out time arg:{}", JSON.toJSONString(paasQueryFilterArg));
        }
        return crmObjectFinalResult;
    }

    // 同步获取所有数据，目前只有目标人群用
    @FilterLog
    public InnerPage<ObjectData> syncListCrmObjectByFilterV4(String ea, Integer fsUserId, PaasQueryFilterArg paasQueryFilterArg) {
        int limit = 1000;
        int offset = 0;
        paasQueryFilterArg.getQuery().addOrderByAsc(ObjectDescribeContants.ID, true);
        paasQueryFilterArg.getQuery().setLimit(limit);
        paasQueryFilterArg.getQuery().setOffset(offset);
        if (StringUtils.isBlank(paasQueryFilterArg.getQuery().getSearchSource())) {
            paasQueryFilterArg.getQuery().setSearchSource(PaasSearchSourceEnum.ES.getValue());
        }
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg.getQuery()));
        findByQueryV3Arg.setDescribeApiName(paasQueryFilterArg.getObjectAPIName());
        findByQueryV3Arg.setSelectFields(paasQueryFilterArg.getSelectFields());
        InnerPage<ObjectData> result = listV3WithRetry(ea, fsUserId, findByQueryV3Arg, 3);
        InnerPage<ObjectData> crmObjectFinalResult = new InnerPage<>();
        List<ObjectData> dataList = new CopyOnWriteArrayList<>();
        crmObjectFinalResult.setDataList(dataList);
        crmObjectFinalResult.setTotalCount(0);
        if (result == null || CollectionUtils.isEmpty(result.getDataList())) {
            log.warn("syncListCrmObjectByFilterV4 first page is empty ea:{} arg: {}", ea, findByQueryV3Arg);
            return crmObjectFinalResult;
        }
        Integer totalCount = result.getTotalCount();
        int page = (totalCount - 1) / limit + 1;
        log.info("syncListCrmObjectByFilterV4 ea: {} totalCount: {} page: {} arg: {} result: {}",ea, totalCount, page, findByQueryV3Arg, getPrintfLog(result));
        crmObjectFinalResult.setTotalCount(totalCount);
        dataList.addAll(result.getDataList());
        for (int i = 2; i <= page; i++) {
            try {
                offset = offset + limit;
                paasQueryFilterArg.getQuery().setLimit(limit);
                paasQueryFilterArg.getQuery().setOffset(offset);
                findByQueryV3Arg.setNeedCount(false);
                findByQueryV3Arg = new FindByQueryV3Arg();
                findByQueryV3Arg.setDescribeApiName(paasQueryFilterArg.getObjectAPIName());
                findByQueryV3Arg.setSelectFields(paasQueryFilterArg.getSelectFields());
                findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg.getQuery()));
                InnerPage<ObjectData> pageResult = listV3WithRetry(ea, fsUserId, findByQueryV3Arg, 3);
                if (pageResult == null || CollectionUtils.isEmpty(pageResult.getDataList())) {
                    log.warn("syncListCrmObjectByFilterV4 page: {} is empty ea:{} arg: {}", i, ea, findByQueryV3Arg);
                    continue;
                }
                log.info("syncListCrmObjectByFilterV4 ea: {} page: {} arg: {} result: {}",ea, page, findByQueryV3Arg, getPrintfLog(pageResult));
                dataList.addAll(pageResult.getDataList());
            } catch (Exception e) {
                log.error("syncListCrmObjectByFilterV4 error ea:{} arg: {}", ea, findByQueryV3Arg, e);
            }
        }
        return crmObjectFinalResult;
    }

    public InnerPage<ObjectData> listV3WithRetry(String ea, Integer fsUserId, FindByQueryV3Arg findByQueryV3Arg, int retryCount) {
        InnerPage<ObjectData> pageResult = null;
        for (int i = 0; i < retryCount; i++) {
            try {
                pageResult = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
                break;
            } catch (Exception e) {
                log.error("listV3WithRetry error ea:{} i: {} arg:{}", ea, i, findByQueryV3Arg, e);
                ThreadUtil.sleepIngore(1000L);
            }
        }
        if (pageResult == null) {
            pageResult = new InnerPage<>();
            pageResult.setTotalCount(0);
            pageResult.setDataList(Lists.newArrayList());
        }
        return pageResult;
    }
    private String getPrintfLog(InnerPage<ObjectData> pageResult) {
        String message = "";
        try {
            int totalCount = pageResult.getTotalCount() == null ? 0 : pageResult.getTotalCount();
            message += " totalCount: " + totalCount;
            if (CollectionUtils.isEmpty(pageResult.getDataList())) {
                return message;
            }
            int size = Math.min(5, pageResult.getDataList().size());
            List<ObjectData> objectDataList = pageResult.getDataList().subList(0, size);
            message += ", first 5 data: " + JsonUtil.toJson(objectDataList);
        } catch (Exception e) {
            log.error("getPrintfLog error", e);
        }
        return message;
    }

    //作废对象
    public List<ObjectData> bulkInvalid(String ea, Integer fsUserId, String apiName, List<String> ids) {
        Result<ActionBulkInvalidResult> result = bulkInvalidWithResult(ea, fsUserId, apiName, ids);
        if (result.getCode() != 0) {
            throw new IllegalStateException("CrmMetadataManager bulkInvalid fail , error result " + result);
        }
        return result.getData().getObjectDataList();
    }

    public Result<ActionBulkInvalidResult> bulkInvalidWithResult(String ea, Integer fsUserId, String apiName, List<String> ids) {
        HeaderObj headerObj = new HeaderObj(eIEAConverter.enterpriseAccountToId(ea), fsUserId);
        ActionBulkInvalidArg arg = new ActionBulkInvalidArg(apiName, ids);
        return metadataActionService.bulkInvalid(headerObj, apiName, arg);
    }

    //作废对象
    public List<ObjectData> bulkInvalidIgnoreError(String ea, Integer fsUserId, String apiName, List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        try {
            HeaderObj headerObj = new HeaderObj(eIEAConverter.enterpriseAccountToId(ea), fsUserId);
            ActionBulkInvalidArg arg = new ActionBulkInvalidArg(apiName, idList);
            Result<ActionBulkInvalidResult> result = metadataActionService.bulkInvalid(headerObj, apiName, arg);
            log.info("bulkInvalidIgnoreError, apiName: {} ea: {} idList: {} result: {}", apiName, ea, idList, result);
            if (result.isSuccess()) {
                return result.getData().getObjectDataList();
            }
            return Lists.newArrayList();
        } catch (Exception e) {
            log.error("bulkInvalidIgnoreError, apiName: {} ea: {} idList: {}", apiName, ea, idList, e);
        }
        return Lists.newArrayList();
    }

    public String addCampaignMembersObj(String ea,  Map<String, Object> dataMap, Integer fsUserId) {
        if (fsUserId == null) {
            fsUserId = -10000;
        }
        ContactData data = new ContactData();
        data.putAll(dataMap);
        data.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        data.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        data.put(CampaignMembersObjApiNameEnum.OWNER.getApiName(), Lists.newArrayList(fsUserId.toString()));
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        try {
            Result<ActionAddResult> result = metadataActionService.add(createHeaderObj(ea, fsUserId), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), false,arg);
            if (result != null && 320001400 == result.getCode()) {
                result = metadataActionService.add(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), false, arg);
            }
            if(!result.isSuccess()) {
                log.warn("CrmV2Manager.addCampaignMembersObj error result:{}", result);
                return null;
            }
            return result.getData().getObjectData().getId();
        } catch (Exception e) {
            log.warn("CrmV2Manager.addCampaignMembersObj error e:{}", e);
            return null;
        }
    }

    public String addCampaignMembersObjByLock(String ea,  Map<String, Object> dataMap, Integer bindObjectType, String bindObjectId, String marketingEventId) {
        ContactData data = new ContactData();
        data.putAll(dataMap);
        data.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        data.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        //data.put(CampaignMembersObjApiNameEnum.OWNER.getApiName(), Lists.newArrayList("-10000"));
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        StringJoiner sj = new StringJoiner("_");
        sj.add(CampaignMergeDataManager.MARKETING_CAMPAIGN_DATA_LOCK_KEY);
        sj.add(ea);
        sj.add(marketingEventId);
        sj.add(bindObjectType.toString());
        sj.add(bindObjectId);
        Integer userId = -10000;
        List<String> userIds = (List<String>) dataMap.get("owner");
        if (CollectionUtils.isNotEmpty(userIds)) {
            userId = Integer.parseInt(userIds.get(0));
        }
        try {
            boolean redisLock = redisManager.lock(sj.toString(), 100);
            if (!redisLock) {
                return null;
            }
            Result<ActionAddResult> result = metadataActionService.add(createHeaderObj(ea, userId), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), false, arg);
            if (result != null && 320001400 == result.getCode()) {
                result = metadataActionService.add(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), false, arg);
            }
            if (!result.isSuccess()) {
                log.info("addCampaignMembersObjByLock add campaign failed arg:{} result:{}", arg, result);
                // 数据已存在再查询一次
                if (result.getCode() == 320001401) {
                    CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(bindObjectType);
                    if (campaignMergeDataObjectTypeEnum == null) {
                        return null;
                    }
                    return getCampaignMembersObjId(ea, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataObjectTypeEnum.getBingObjectId(), bindObjectId,
                        marketingEventId);
                }
                log.warn("CrmV2Manager.addCampaignMembersObj error result:{}", result);
                return null;
            }
            return result.getData().getObjectData().getId();
        } catch (Exception e) {
            redisManager.unLock(sj.toString());
            log.warn("CrmV2Manager.addCampaignMembersObj error e:{}", e);
            return null;
        }
    }

    public String addCampaignMembersObjWithoutLock(String ea,  Map<String, Object> dataMap, Integer bindObjectType, String bindObjectId, String marketingEventId) {
        ContactData data = new ContactData();
        data.putAll(dataMap);
        data.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        data.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        //data.put(CampaignMembersObjApiNameEnum.OWNER.getApiName(), Lists.newArrayList("-10000"));
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        StringJoiner sj = new StringJoiner("_");
        sj.add(CampaignMergeDataManager.MARKETING_CAMPAIGN_DATA_LOCK_KEY);
        sj.add(ea);
        sj.add(marketingEventId);
        sj.add(bindObjectType.toString());
        sj.add(bindObjectId);
        Integer userId = -10000;
        List<String> userIds = (List<String>) dataMap.get("owner");
        if (CollectionUtils.isNotEmpty(userIds)) {
            userId = Integer.parseInt(userIds.get(0));
        }
        try {
            Result<ActionAddResult> result = metadataActionService.add(createHeaderObj(ea, userId), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), false, arg);
            if (result != null && 320001400 == result.getCode()) {
                result = metadataActionService.add(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), false, arg);
            }
            return result.getData().getObjectData().getId();
        } catch (Exception e) {
            log.warn("CrmV2Manager.addCampaignMembersObjWithoutLock error e:{}", e);
            return null;
        }
    }

    public String getCampaignMembersObjId(String ea, String objectApiName, String objectIdApiName, String objectId, String marketingEventId) {
        Integer ei = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj searchHeader = new HeaderObj(ei, -10000);

        Filter objectApiNameFilter = new Filter();
        objectApiNameFilter.setFieldName(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName());
        objectApiNameFilter.setOperator(Filter.OperatorContants.EQ);
        objectApiNameFilter.setFieldValues(Lists.newArrayList(objectApiName));

        Filter objectIdApiNameFilter = new Filter();
        objectIdApiNameFilter.setFieldName(objectIdApiName);
        objectIdApiNameFilter.setOperator(Filter.OperatorContants.EQ);
        objectIdApiNameFilter.setFieldValues(Lists.newArrayList(objectId));

        Filter marketingEventIdFilter = new Filter();
        marketingEventIdFilter.setFieldName(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName());
        marketingEventIdFilter.setOperator(Filter.OperatorContants.EQ);
        marketingEventIdFilter.setFieldValues(Lists.newArrayList(marketingEventId));

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1);
        searchQuery.setFilters(Lists.newArrayList(objectApiNameFilter, objectIdApiNameFilter, marketingEventIdFilter));
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setSearchQuery(searchQuery);

        try {
            Result<Page<ObjectData>> objectResult = metadataControllerService.list(searchHeader, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), controllerListArg);
            if (!objectResult.isSuccess() || objectResult.getData() == null || CollectionUtils.isEmpty(objectResult.getData().getDataList())) {
                return null;
            }
            ObjectData objectData = objectResult.getData().getDataList().get(0);
            return objectData.getId();
        } catch (Exception e) {
            log.warn("CrmV2Manager.getCampaignMembersObjId objectResult error e:{}", e);
        }
        return null;
    }

    public void editCampaignMembersObj(String ea, String objectId, Map<String, Object> dataMap) {
        Integer ei = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj headerObj = new HeaderObj(ei, -10000);
        ActionEditArg actionEditArg = new ActionEditArg();
        ObjectData objectData = new ObjectData(CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName());
        objectData.setTenantId(ei);
        objectData.put("_id", objectId);
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            objectData.put(entry.getKey(), entry.getValue());
        }
        actionEditArg.setObjectData(objectData);
        metadataActionService.edit(headerObj, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), true, true, actionEditArg);
    }


    public List<ObjectData> getCampaignMembersObjByFilter(String ea, Integer userId, List<Filter> filterList, String marketingEventId, Boolean needAllData) {
        Integer pageNum = 10000;
        Integer pageSize = CampaignConstants.MAX_QUERY_SIZE_FROM_CRM_ONCE;
        if (needAllData) {
            List<ObjectData> allObjectData = Lists.newArrayList();
            for (int i = 1; i <= pageNum; i++) {
                List<ObjectData> objectDataList = getCampaignMembersObjByFilterByPage(ea, userId, filterList, marketingEventId, ((i - 1) * pageSize), pageSize);
                if (CollectionUtils.isEmpty(objectDataList)) {
                    return allObjectData;
                } else {
                    allObjectData.addAll(objectDataList);
                }
            }
            return allObjectData;
        } else {
            return getCampaignMembersObjByFilterByPage(ea, userId, filterList, marketingEventId, 0, pageSize);
        }
       /* List<Filter> allFilterList = Lists.newArrayList();
        Integer ei = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj searchHeader = new HeaderObj(ei, userId);
        if (CollectionUtils.isNotEmpty(filterList)) {
            allFilterList.addAll(filterList);
        }
        Filter marketingEventIdFilter = new Filter();
        marketingEventIdFilter.setFieldName(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName());
        marketingEventIdFilter.setOperator(Filter.OperatorContants.EQ);
        marketingEventIdFilter.setFieldValues(Lists.newArrayList(marketingEventIds));

        allFilterList.add(marketingEventIdFilter);

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10000);
        searchQuery.setFilters(allFilterList);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setIncludeLayout(false);
        controllerListArg.setIncludeButtonInfo(false);
        controllerListArg.setSearchQuery(searchQuery);
        *//*if (CollectionUtils.isNotEmpty(fieldsProjection)) {
            controllerListArg.setFieldProjection(fieldsProjection);
        }*//*

        try {
            Result<Page<ObjectData>> objectResult = metadataControllerService.list(searchHeader, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), controllerListArg);
            if (!objectResult.isSuccess() || objectResult.getData() == null || CollectionUtils.isEmpty(objectResult.getData().getDataList())) {
                return Lists.newArrayList();
            }
            return objectResult.getData().getDataList();
        } catch (Exception e) {
            log.warn("CrmV2Manager.getCampaignMembersObjByFilter error e:{}", e);
            return Lists.newArrayList();
        }*/
    }


    private List<ObjectData> getCampaignMembersObjByFilterByPage(String ea, Integer userId, List<Filter> filterList, String marketingEventId, Integer offset, Integer limit) {
        List<Filter> allFilterList = Lists.newArrayList();
        Integer ei = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj searchHeader = new HeaderObj(ei, userId);
        if (CollectionUtils.isNotEmpty(filterList)) {
            allFilterList.addAll(filterList);
        }
        Filter marketingEventIdFilter = new Filter();
        marketingEventIdFilter.setFieldName(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName());
        marketingEventIdFilter.setOperator(Filter.OperatorContants.EQ);
        marketingEventIdFilter.setFieldValues(Lists.newArrayList(marketingEventId));

        allFilterList.add(marketingEventIdFilter);

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(offset);
        searchQuery.setLimit(limit);
        searchQuery.setFilters(allFilterList);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
        controllerListArg.setSearchQuery(searchQuery);

        try {
            Result<Page<ObjectData>> objectResult = metadataControllerService.list(searchHeader, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), controllerListArg);
            if (!objectResult.isSuccess() || objectResult.getData() == null || CollectionUtils.isEmpty(objectResult.getData().getDataList())) {
                return Lists.newArrayList();
            }
            return objectResult.getData().getDataList();
        } catch (Exception e) {
            log.warn("CrmV2Manager.getCampaignMembersObjByFilter error e:{}", e);
            return Lists.newArrayList();
        }
    }


    public Integer getObjTotalCountByFilter(String ea, Integer userId, String apiName, List<Filter> filterList) {
        Integer ei = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj searchHeader = new HeaderObj(ei, userId);

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1);
        searchQuery.setFilters(filterList);
        searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setSearchQuery(searchQuery);
        try {
            Result<Page<ObjectData>> objectResult = metadataControllerService.list(searchHeader, apiName, controllerListArg);
            if (!objectResult.isSuccess() || objectResult.getData() == null || CollectionUtils.isEmpty(objectResult.getData().getDataList())) {
                return 0;
            }
            return objectResult.getData().getTotal();
        } catch (Exception e) {
            log.warn("CrmV2Manager.getObjTotalCountByFilter error e:{}", e);
        }
        return 0;
    }
    
    public ObjectData convertObjectDataByFieldMapping(String ea, String fromObjectApiName, String fromObjectId, String toObjectApiName, FieldMappings fieldMappings){
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.fromName(toObjectApiName));
        if (CollectionUtils.isEmpty(crmUserDefineFieldVoList)) {
            return new ObjectData(toObjectApiName);
        }
        Map<String, String> fieldTypeMap = crmUserDefineFieldVoList.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, CrmUserDefineFieldVo::getFieldTypeName, (v1, v2) -> v1));
        Set<String> notNullCrmMemberFieldNames = crmUserDefineFieldVoList.stream().filter(field -> BooleanUtils.isTrue(field.getIsNotNull())
                && !CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName().equals(field.getFieldName())).map(CrmUserDefineFieldVo::getFieldName)
            .collect(Collectors.toSet());
        boolean verifyResult = fieldMappings.doVerifyCrmNotNullFields(notNullCrmMemberFieldNames);
        if (!verifyResult) {
            return null;
        }
        ObjectData fromObjectData = getDetail(ea, -10000, fromObjectApiName, fromObjectId);
        if (fromObjectApiName == null){
            return null;
        }
        ObjectData toObjectData = new ObjectData(toObjectApiName);
        for (FieldMappings.FieldMapping fieldMapping : fieldMappings) {
            if (StringUtils.isBlank(fieldMapping.getCrmFieldName())) {
                continue;
            }
            String fieldType = fieldTypeMap.get(fieldMapping.getCrmFieldName());
            if (Strings.isNullOrEmpty(fieldMapping.getMankeepFieldName())) {
                if (StringUtils.isNotBlank(fieldType) && fieldType.equals(CrmV2FieldTypeEnum.SelectMany.getName())) {
                    toObjectData.put(fieldMapping.getCrmFieldName(), Lists.newArrayList(fieldMapping.getDefaultValue()));
                } else {
                    toObjectData.put(fieldMapping.getCrmFieldName(), fieldMapping.getDefaultValue());
                }
            } else {
                String crmFieldName = fieldMapping.getCrmFieldName();
                Object value = fromObjectData.get(fieldMapping.getMankeepFieldName());
                if (value == null && notNullCrmMemberFieldNames.contains(crmFieldName)
                        && Lists.newArrayList(CrmV2FieldTypeEnum.Text.getName(), CrmV2FieldTypeEnum.LongText.getName()).contains(fieldType)) {
                    toObjectData.put(crmFieldName, "-");
                } else if (value == null && notNullCrmMemberFieldNames.contains(crmFieldName)
                        && Lists.newArrayList(CrmV2FieldTypeEnum.Email.getName()).contains(fieldType)) {
                    toObjectData.put(crmFieldName, "<EMAIL>");
                } else {
                    if (Lists.newArrayList(CrmV2FieldTypeEnum.SelectOne.getName()).contains(fieldType) && fieldMapping.getValuesOptions() != null) {
                        toObjectData.put(crmFieldName, fieldMapping.getValuesOptions().get(value+""));
                    } else {
                        toObjectData.put(crmFieldName, value);
                        //表单字段映射对象
                        if (StringUtils.isBlank(fieldMapping.getDefaultValue())
                                && StringUtils.isNotBlank(fieldMapping.getMappingObjectFieldName())
                                && StringUtils.isNotBlank(fieldMapping.getMappingObjectApiName())) {
                            ObjectData objectData = this.getDetailByApiNameAndField(ea,fieldMapping.getMappingObjectApiName(),fieldMapping.getMappingObjectFieldName(), value);
                            if (objectData == null) {
                                toObjectData.remove(crmFieldName);
                            } else {
                                toObjectData.put(crmFieldName, objectData.getId());
                            }
                        }
                    }
                }
            }
        }
        return toObjectData;
    }
    
    public boolean markWxFanSpreadUserAsSingle(String ea){
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        HeaderObj systemHeader = new HeaderObj(ei, -10000);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.WECHAT.getName());
        if(!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null){
            return false;
        }
    
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
    
        if (fieldMap.get(CrmWechatFanFieldEnum.SPREAD_FS_USER_ID.getFieldName()) != null){
            AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
            addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT.getName());
            addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"WechatFanObj\",\"define_type\":\"package\",\"is_index\": true,\"is_active\": true,\"is_unique\": false,\"label\": \"推广人\",\"type\": \"employee\",\"is_required\": false,\"api_name\": \"spread_fs_user_id\",\"status\": \"new\",\"is_single\":true}");
            addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"employee\",\"api_name\":\"WechatFanObj_default_layout__c\",\"label\":\"推广人\",\"is_default\":true}]");
            objectDescribeCrmService.updateCustomFieldDescribe(systemHeader, addDescribeCustomFieldArg);
            return true;
        }
        return false;
    }

    /**
     * 获取对象中某个字段的描述
     * @param ei
     * @param apiName
     * @param fieldName
     * @return
     */
    public FieldDescribe getFieldDescribe(Integer ei, String apiName, String fieldName){
        Result<ControllerGetDescribeResult> describeResult = objectDescribeService.getDescribe(HeaderObj.newInstance(ei, -10000), apiName);
        HashMap<String, FieldDescribe> relationFieldDescribeHashMap = describeResult.getData().getDescribe().getFields();
        FieldDescribe relationFieldDescribe = relationFieldDescribeHashMap.get(fieldName);
        return relationFieldDescribe;
    }

    public ObjectDataQueryListByIdsResult getRelatedDataList(String ea, String apiName, GetRelatedDataListArg arg) {
        arg.setIncludeAssociated(true);
        Integer ei = eIEAConverter.enterpriseAccountToId(ea);
        Result<ObjectDataQueryListByIdsResult> dataList = metadataControllerService.getRelatedDataList(new HeaderObj(ei, -10000), apiName, arg);
        if (!dataList.isSuccess()) {
            log.warn("CrmV2Manager.getRelatedDataList error result :{}", dataList);
            return null;
        }
        return dataList.getData();
    }
    
    public boolean tryAddFieldsToWechatFans(String ea){
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        HeaderObj systemHeader = new HeaderObj(ei, -10000);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.WECHAT.getName());
        if(!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null){
            return false;
        }
    
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
    
        if (fieldMap.get(CrmWechatFanFieldEnum.SPREAD_FS_USER_ID.getFieldName()) == null){
            AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
            addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT.getName());
            addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"WechatFanObj\",\"define_type\":\"package\",\"is_index\": true,\"is_active\": true,\"is_unique\": false,\"label\": \"推广人\",\"type\": \"employee\",\"is_required\": false,\"api_name\": \"spread_fs_user_id\",\"status\": \"new\",\"is_single\":true}");
            addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"employee\",\"api_name\":\"WechatFanObj_default_layout__c\",\"label\":\"推广人\",\"is_default\":true}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
        }
    
        if (fieldMap.get(CrmWechatFanFieldEnum.MARKETING_EVENT_ID.getFieldName()) == null){
            AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
            addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT.getName());
            addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"WechatFanObj\",\"define_type\":\"package\",\"is_index\": true,\"is_active\": true,\"is_unique\": false,\"label\": \"市场活动\",\"type\": \"object_reference\",\"is_required\": false,\"api_name\": \"marketing_event_id\",\"status\": \"new\", \"target_api_name\": \"MarketingEventObj\", \"target_related_list_label\": \"微信用户\", \"target_related_list_name\": \"target_related_list_N87T7__c\", \"action_on_target_delete\": \"set_null\"}");
            addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"WechatFanObj_default_layout__c\",\"label\":\"市场活动\",\"is_default\":true}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
        }
    
        if (fieldMap.get(CrmWechatFanFieldEnum.MARKETING_ACTIVITY_ID.getFieldName()) == null){
            AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
            addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT.getName());
            addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"WechatFanObj\",\"define_type\":\"package\",\"is_index\": true,\"is_active\": true,\"is_unique\": false,\"label\": \"营销活动\",\"type\": \"object_reference\",\"is_required\": false,\"api_name\": \"marketing_activity_id\",\"status\": \"new\", \"target_api_name\": \"MarketingActivityObj\", \"target_related_list_label\": \"微信用户\", \"target_related_list_name\": \"target_related_list_N87T8__c\", \"action_on_target_delete\": \"set_null\"}");
            addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"WechatFanObj_default_layout__c\",\"label\":\"营销活动\",\"is_default\":true}]");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
        }
        return true;
    }

    /**
     * 销售线索查重
     * @param ea
     * @param data
     * @return 是否重复
     */
    public com.facishare.marketing.common.result.Result<LeadDuplicateSearchResult> leadDuplicateSearchByObject(String ea, Map<String, Object> data) {
        LeadDuplicateSearchResult leadDuplicateSearchResult = new LeadDuplicateSearchResult();
        DuplicatesearchQueryResult duplicatesearchQueryResult = queryDuplicate(ea, -10000, CrmObjectApiNameEnum.CRM_LEAD.getName(), data);
        if (duplicatesearchQueryResult == null) {
            log.warn("CrmV2Manager.leadDuplicateSearchByObject error duplicatesearchQueryResult is null");
            return com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        List<Map<String, Object>> duplicateDatas = new ArrayList<>();

        if (duplicatesearchQueryResult.getData() != null) {
            duplicateDatas.addAll(duplicatesearchQueryResult.getData());
        }
        if (duplicatesearchQueryResult.getDuplicateMode() == CrmDuplicateSearchVo.DuplicateMode.SAME) {

            RelatedDuplicateSearchResult duplicateSearchResult = this.relatedDuplicateSearch(ea, LeadsFieldContants.API_NAME, data, 1, 10000, null);
            if (duplicateSearchResult != null) {
                log.warn("CrmV2Manager.leadDuplicateSearchByObject duplicateSearchResult {}", JSONObject.toJSONString(duplicateSearchResult));
                List<RelatedDuplicateSearchResult.DataList> results = duplicateSearchResult.getResults();
                if (results != null) {
                    List<Map<String, Object>> reduce = results.stream()
                            .map(RelatedDuplicateSearchResult.DataList::getDataList)
                            .reduce(new ArrayList<>(), (all, item) -> {
                                all.addAll(item);
                                return all;
                            });
                    duplicateDatas.addAll(reduce);
                }
            }

            if (CollectionUtils.isNotEmpty(duplicateDatas)) {
                log.info("CrmV2Manager.leadDuplicateSearchByObject duplicatesearchQueryResultDatas:{}", JSONObject.toJSONString(duplicatesearchQueryResult.getData()));
                Map<String, List<Map<String, Object>>> categoryObjectData = duplicateDatas.stream()
                        .peek(e->{
                            if ("invalid".equals(e.get(CrmV2LeadFieldEnum.LifeStatus.getNewFieldName()))) {
                                e.put("priority", 0);
                            } else {
                                e.put("priority", 1);
                            }
                        })
                        .sorted(Comparator.comparing(CustomizeFormDataManager::getTime).reversed())
                        .sorted(Comparator.comparing(CustomizeFormDataManager::getPriority).reversed())
                        .collect(Collectors.groupingBy(e -> String.valueOf(e.get("object_describe_api_name"))));
                Map<String, Object> objMap = null;
                if (categoryObjectData.get(CrmObjectApiNameEnum.CUSTOMER.getName()) != null) {
                    objMap = categoryObjectData.get(CrmObjectApiNameEnum.CUSTOMER.getName()).get(0);
                } else if (categoryObjectData.get(CrmObjectApiNameEnum.CONTACT.getName()) != null) {
                    objMap = categoryObjectData.get(CrmObjectApiNameEnum.CONTACT.getName()).get(0);
                } else if (categoryObjectData.get(CrmObjectApiNameEnum.CRM_LEAD.getName()) != null) {
                    objMap = categoryObjectData.get(CrmObjectApiNameEnum.CRM_LEAD.getName()).get(0);
                }
                if (objMap != null) {
                    leadDuplicateSearchResult.setDuplicate(false);
                    leadDuplicateSearchResult.setLeadId(String.valueOf(objMap.get(CrmV2LeadFieldEnum.ID.getNewFieldName())));
                    leadDuplicateSearchResult.setCrmObjectType(String.valueOf(objMap.get("object_describe_api_name")));
                    return com.facishare.marketing.common.result.Result.newSuccess(leadDuplicateSearchResult);
                }
            } else {
                leadDuplicateSearchResult.setDuplicate(true);
                return com.facishare.marketing.common.result.Result.newSuccess(leadDuplicateSearchResult);
            }
        } else if (duplicatesearchQueryResult.getDuplicateMode() == CrmDuplicateSearchVo.DuplicateMode.LIKE) {
            leadDuplicateSearchResult.setDuplicate(true);
            return com.facishare.marketing.common.result.Result.newSuccess(leadDuplicateSearchResult);
        }
        leadDuplicateSearchResult.setDuplicate(false);
        return com.facishare.marketing.common.result.Result.newSuccess(leadDuplicateSearchResult);
    }

    /**
     * 线索查重
     * @param ea
     * @param data
     * @return 是否重复
     */
    public com.facishare.marketing.common.result.Result<LeadDuplicateSearchResult> leadDuplicateSearchByObjectV2(String ea, Map<String, Object> data) {
        LeadDuplicateSearchResult leadDuplicateSearchResult = new LeadDuplicateSearchResult();
        DuplicatesearchQueryResult duplicatesearchQueryResult = queryDuplicate(ea, -10000, CrmObjectApiNameEnum.CRM_LEAD.getName(), data);
        if (duplicatesearchQueryResult == null) {
            log.warn("CrmV2Manager.leadDuplicateSearchByObjectV2 error duplicatesearchQueryResult is null");
            return com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        List<Map<String, Object>> duplicateDatas = new ArrayList<>();

        if (duplicatesearchQueryResult.getData() != null) {
            duplicateDatas.addAll(duplicatesearchQueryResult.getData());
        }
        if (duplicatesearchQueryResult.getDuplicateMode() == CrmDuplicateSearchVo.DuplicateMode.SAME) {

            RelatedDuplicateSearchResult duplicateSearchResult = this.relatedDuplicateSearch(ea, LeadsFieldContants.API_NAME, data, 1, 10000, null);
            if (duplicateSearchResult != null) {
                log.warn("CrmV2Manager.leadDuplicateSearchByObjectV2 duplicateSearchResult {}", JSONObject.toJSONString(duplicateSearchResult));
                List<RelatedDuplicateSearchResult.DataList> results = duplicateSearchResult.getResults();
                if (results != null) {
                    List<Map<String, Object>> reduce = results.stream()
                            .map(RelatedDuplicateSearchResult.DataList::getDataList)
                            .reduce(new ArrayList<>(), (all, item) -> {
                                all.addAll(item);
                                return all;
                            });
                    duplicateDatas.addAll(reduce);
                }
            }

            if (CollectionUtils.isNotEmpty(duplicateDatas)) {
                log.info("CrmV2Manager.leadDuplicateSearchByObjectV2 duplicatesearchQueryResultDatas:{}", JSONObject.toJSONString(duplicatesearchQueryResult.getData()));
                Map<String, List<Map<String, Object>>> categoryObjectData = duplicateDatas.stream()
                        .peek(e->{
                            if ("invalid".equals(e.get(CrmV2LeadFieldEnum.LifeStatus.getNewFieldName()))) {
                                e.put("priority", 0);
                            } else {
                                e.put("priority", 1);
                            }
                        })
                        .sorted(Comparator.comparing(CustomizeFormDataManager::getTime).reversed())
                        .sorted(Comparator.comparing(CustomizeFormDataManager::getPriority).reversed())
                        .collect(Collectors.groupingBy(e -> String.valueOf(e.get("object_describe_api_name"))));
                Map<String, Object> objMap = null;
                if (categoryObjectData.get(CrmObjectApiNameEnum.CRM_LEAD.getName()) != null) {
                    objMap = categoryObjectData.get(CrmObjectApiNameEnum.CRM_LEAD.getName()).get(0);
                }
                if (objMap != null) {
                    leadDuplicateSearchResult.setDuplicate(false);
                    leadDuplicateSearchResult.setLeadId(String.valueOf(objMap.get(CrmV2LeadFieldEnum.ID.getNewFieldName())));
                    leadDuplicateSearchResult.setCrmObjectType(String.valueOf(objMap.get("object_describe_api_name")));
                    return com.facishare.marketing.common.result.Result.newSuccess(leadDuplicateSearchResult);
                }
            } else {
                leadDuplicateSearchResult.setDuplicate(true);
                return com.facishare.marketing.common.result.Result.newSuccess(leadDuplicateSearchResult);
            }
        } else if (duplicatesearchQueryResult.getDuplicateMode() == CrmDuplicateSearchVo.DuplicateMode.LIKE) {
            leadDuplicateSearchResult.setDuplicate(true);
            return com.facishare.marketing.common.result.Result.newSuccess(leadDuplicateSearchResult);
        }
        leadDuplicateSearchResult.setDuplicate(false);
        return com.facishare.marketing.common.result.Result.newSuccess(leadDuplicateSearchResult);
    }

    public Result<ActionEditResult> updateLeadObj(String ea,String leadId, Integer spreadFsUid,Integer fsUserId) {
        if (fsUserId == null) {
            fsUserId = -10000;
        }
        ActionEditArg arg = new ActionEditArg();
        ObjectData data = new ObjectData();
        data.put(CrmV2LeadFieldEnum.ID.getNewFieldName(),leadId);
        List<String> cretors = new ArrayList<>();
        cretors.add(String.valueOf(spreadFsUid));
        data.put("created_by",cretors);
        arg.setObjectData(data);
        Result<ActionEditResult> result =  metadataActionService.edit(createHeaderObj(ea, fsUserId), LeadsFieldContants.API_NAME, true, true, arg);
        return result;
    }

    public CreateObjResult updateCrmObj(String ea, Integer fsUserId,String extraDataId, Map<String, Object> fieldDataMap,String crmApiName) {
        ContactData data = new ContactData();
        data.putAll(fieldDataMap);
        data.put(CrmV2LeadFieldEnum.ID.getNewFieldName(), extraDataId);
        CreateObjResult createObjResult = new CreateObjResult();
        HeaderObj headerObj = createHeaderObj(ea, fsUserId);
        headerObj.put("x-fs-peer-name","fs-marketing");
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(data);
        Result<ActionEditResult> result = metadataActionService.edit(headerObj, crmApiName, true, true, arg);
        if (result != null && result.getCode() == Result.SUCCESS_CODE) {
            createObjResult.setId((String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName()));
            createObjResult.setMessage(result.getMessage());
        } else {
            createObjResult.setMessage(result != null ? result.getMessage() : I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
        }
        return createObjResult;
    }

    public CreateObjResult createCrmObj(String ea, Integer fsUserId, Map<String, Object> fieldDataMap, String crmApiName) {
        ContactData data = new ContactData();
        data.putAll(fieldDataMap);
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
        optionInfo.setCalculateDefaultValue(true);
        arg.setOptionInfo(optionInfo);
        CreateObjResult createObjResult = new CreateObjResult();
        HeaderObj headerObj = createHeaderObj(ea, fsUserId);
        headerObj.put("x-fs-peer-name","fs-marketing");
        Result<ActionAddResult> result = metadataActionService.add(headerObj, crmApiName, false, arg);
        if (result != null && result.getCode() == Result.SUCCESS_CODE) {
            createObjResult.setId((String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName()));
            createObjResult.setMessage(result.getMessage());
        } else {
            createObjResult.setMessage(result != null ? result.getMessage() : I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
        }
        return createObjResult;
    }

    @FilterLog
    public ObjectDescribe getCrmDescribeDetail(String ea, String apiName) {
        try {
            Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null), apiName);
            if (result.isSuccess() && result.getData() != null) {
                return result.getData().getDescribe();
            }
        } catch (Exception e) {
            log.warn("getCrmDescribeDetail is error",e);
        }
        return null;
    }

    public ObjectData getDetailByApiNameAndField(String ea, String mappingObjectApiName, String mappingObjectFieldName, Object fieldValue) {
        PaasQueryFilterArg filterArg = new PaasQueryFilterArg();
        filterArg.setSelectFields(Lists.newArrayList("_id"));
        filterArg.setObjectAPIName(mappingObjectApiName);
        PaasQueryArg query = new PaasQueryArg(0,1);
        query.addFilter(mappingObjectFieldName,PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(),Lists.newArrayList((String) fieldValue));
        query.addOrderByAsc("last_modified_time",false);
        filterArg.setQuery(query);
        InnerPage<ObjectData> objectDataInnerPage = this.listCrmObjectScanByIdV3(ea, 1000, filterArg, null, 1);
        if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
            return objectDataInnerPage.getDataList().get(0);
        }
        return null;
    }

    public List<CrmUserDefineFieldVo> getCrmObjectMultilevelDescribesListByObjectApiName(String ea, String objectApiName) {
        Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null),objectApiName);
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = new ArrayList<>();
        for (Map.Entry<String, FieldDescribe> entry : result.getData().getDescribe().getFields().entrySet()) {
            FieldDescribe fieldDescribe = entry.getValue();
            if (objectFieldMultilevelDescribesFilterByObjectApiName(objectApiName, fieldDescribe)) {
                continue;
            }

            // 过滤系统字段
            if (objectApiName.equals(CrmObjectApiNameEnum.MEMBER.getName())){
                if("system".equals(fieldDescribe.getDefineType()) && !"name".equals(fieldDescribe.getApiName())){
                    continue;
                }
                ImmutableSet<String> excludeFields = ImmutableSet.of("lock_rule", "extend_obj_data_id", "life_status_before_invalid", "owner_department", "integral_value", "area_location", "lock_status",
                        "growth_value", "relevant_team", "lock_user", "grade_id", "owner", "life_status", "record_type", "customer_id", "location");
                if (excludeFields.contains(fieldDescribe.getApiName())){
                    continue;
                }
            }

            // 去除作废字段
            if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
                continue;
            }

            // 过滤无法处理字段
            if (StringUtils.isNotBlank(fieldDescribe.getApiName()) && CrmV2FieldTypeEnum.IllegalApiName(fieldDescribe.getApiName())) {
                continue;
            }

            CrmUserDefineFieldVo crmUserDefineFieldVo = fieldDescribeToDefineVo(fieldDescribe);
            crmUserDefineFieldVo.setFieldTypeName(fieldDescribe.getType());
            crmUserDefineFieldVoList.add(crmUserDefineFieldVo);
        }

        return crmUserDefineFieldVoList;
    }

    private boolean objectFieldMultilevelDescribesFilterByObjectApiName(String objectApiName, FieldDescribe fieldDescribe) {
        String apiName = fieldDescribe.getApiName();
        if (StringUtils.isBlank(apiName)) {
            return false;
        }
        //去除查找关联字段
        if (Objects.equals(CrmV2FieldTypeEnum.ObjectReference.getName(),fieldDescribe.getType())) {
            return true;
        }
        String defineType = (String) fieldDescribe.get("define_type");
        if (StringUtils.isNotBlank(defineType) && defineType.equals("custom")) {
            return false;
        }

        if (apiName.startsWith("UD")) {
            return false;
        }

        // 去除作废字段
        if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
            return true;
        }

        if (objectApiName.equals(CrmObjectApiNameEnum.CRM_LEAD.getName())) {
            Boolean needFilter = CrmV2LeadFieldEnum.needFilter(apiName);
            if (null == needFilter || needFilter) {
                return true;
            }
        } else if (objectApiName.equals(CrmObjectApiNameEnum.CUSTOMER.getName())) {
            Boolean needFilter = CrmV2CustomerFieldEnum.needFilter(apiName);
            if (null == needFilter || needFilter) {
                return true;
            }
        } else if (objectApiName.equals(CrmObjectApiNameEnum.CONTACT.getName())) {
            Boolean needFilter = CrmV2ContactFieldEnum.needFilter(apiName);
            if (null == needFilter || needFilter) {
                return true;
            }
        }else if (objectApiName.equals(CrmObjectApiNameEnum.MARKETING_EVENT.getName())){
            Boolean needFilter = CrmV2MarketingEventFilterEnum.needFilter(apiName);
            if (needFilter != null && needFilter) {
                return true;
            }
        } else {
            Boolean needFilter = CrmV2ObjectFilterEnum.needFilter(apiName);
            if (needFilter != null && needFilter) {
                return true;
            }
        }
        return false;
    }

    @Data
    public static class LeadDuplicateSearchResult implements Serializable {

        private boolean duplicate;

        private String leadId;

        private String crmObjectType;

    }


    public Result<ActionAddResult> addMarketingActivityObj(String ea, Map<String, Object> dataMap, Integer fsUserId) {
        if (fsUserId == null) {
            fsUserId = -10000;
        }
        ContactData data = new ContactData();
        data.putAll(dataMap);
        data.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        data.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        return metadataActionService.add(createHeaderObj(ea, fsUserId), CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), false, arg);

    }

    public String updateMarketingActivityObj(String ea, Map<String, Object> dataMap, Integer fsUserId) {
        if (fsUserId == null) {
            fsUserId = -10000;
        }
        Integer ei = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj headerObj = new HeaderObj(ei, fsUserId);
        ActionEditArg actionEditArg = new ActionEditArg();
        ObjectData objectData = new ObjectData(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        objectData.setTenantId(ei);
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            objectData.put(entry.getKey(), entry.getValue());
        }
        actionEditArg.setObjectData(objectData);
        try {
            Result<ActionEditResult> result = metadataActionService.edit(headerObj, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), true, true, actionEditArg);
            if (!result.isSuccess()) {
                log.warn("CrmV2Manager.updateMarketingActivityObj error result:{}", result);
                return null;
            }
            return result.getData().getObjectData().getId();
        } catch (Exception e) {
            log.warn("CrmV2Manager.updateMarketingActivityObj error e:{}", e);
            return null;
        }
    }

    public String getCrmIdByMarketingUserId(String crmObjectApiName, String ea, String marketingUserId) {
        if (StringUtils.isEmpty(crmObjectApiName)) {
            return null;
        }
        CrmObjectApiNameEnum crmObjectApiNameEnum = CrmObjectApiNameEnum.fromName(crmObjectApiName);
        if (crmObjectApiNameEnum == null) {
            return null;
        }
        String crmId = null;
        switch (Objects.requireNonNull(crmObjectApiNameEnum)) {
            case CRM_LEAD:
                UserMarketingCrmLeadAccountRelationEntity leadEntity = userMarketingCrmLeadAccountRelationDao.getEachDataLastByUserMarketingId(ea, marketingUserId);
                crmId = leadEntity == null ? null : leadEntity.getCrmLeadId();
                break;
            case CUSTOMER:
                UserMarketingCrmAccountAccountRelationEntity customerEntity = userMarketingCrmAccountAccountRelationDao.getEachDataLastByUserMarketingId(ea, marketingUserId);
                crmId = customerEntity == null ? null : customerEntity.getCrmAccountId();
                break;
            case CONTACT:
                UserMarketingCrmContactAccountRelationEntity contactEntity = userMarketingCrmContactAccountRelationDao.getEachDataLastByUserMarketingId(ea, marketingUserId);
                crmId = contactEntity == null ? null : contactEntity.getCrmContactId();
                break;
            case WECHAT:
                UserMarketingCrmWxUserAccountRelationEntity wechatEntity = userMarketingCrmWxUserAccountRelationDao.getEachDataLastByUserMarketingId(ea, marketingUserId);
                crmId = wechatEntity == null ? null : wechatEntity.getCrmWxUserId();
                break;
            case MEMBER:
                UserMarketingCrmMemberRelationEntity memberEntity = userMarketingCrmMemberRelationDao.getEachDataLastByUserMarketingId(ea, marketingUserId);
                crmId = memberEntity == null ? null : memberEntity.getCrmMemberObjectId();
                break;
            case WECHAT_WORK_EXTERNAL_USER_OBJ:
                UserMarketingCrmWxWorkExternalUserRelationEntity wechatWorkExternalUserObjEntity = userMarketingCrmWxWorkExternalUserRelationDao
                    .getEachDataLastByUserMarketingId(ea, marketingUserId);
                crmId = wechatWorkExternalUserObjEntity == null ? null : wechatWorkExternalUserObjEntity.getCrmWxWorkExternalUserObjectId();
                break;
            default:
                break;
        }
        return crmId;
    }


    public List<ObjectData> getMarketingEventTreeByPath(String ea, String path, String marketingEventId, Boolean needFilterCurrentData) {
        List<ObjectData> objectDataList = Lists.newArrayList();
        GetTreeByPathArg arg = new GetTreeByPathArg();
        arg.setMarketingEventId(marketingEventId);
        arg.setFilter(false);
        try {
            Result<ObjectDataQueryListByIdsResult> treeByPath = metadataControllerService.getTreeByPath(createHeaderObj(ea, -10000), path, arg);
            if (!treeByPath.isSuccess() || treeByPath.getData() == null) {
                log.warn("CrmV2Manager.getMarketingEventTreeByPath not success result:{}", treeByPath);
                return objectDataList;
            }
            if (needFilterCurrentData) {
                for (ObjectData objectData : treeByPath.getData().getDataList()) {
                    if (!objectData.getId().equals(marketingEventId)) {
                        objectDataList.add(objectData);
                    }
                }
                return objectDataList;
            }
            return treeByPath.getData().getDataList();
        } catch (Exception e) {
            log.warn("CrmV2Manager.getMarketingEventTreeByPath error e:", e);
        }
        return objectDataList;
    }

    public List<CrmUserDefineFieldVo> getObjectCustomizeFieldDescribesList(String ea, CrmObjectApiNameEnum crmObjectApiNameEnum) {
        Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(createHeaderObj(ea, null), crmObjectApiNameEnum.getName());
        List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = new ArrayList<>();
        for (Map.Entry<String, FieldDescribe> entry : result.getData().getDescribe().getFields().entrySet()) {
            FieldDescribe fieldDescribe = entry.getValue();
            if (objectFieldDescribesFilter(crmObjectApiNameEnum, fieldDescribe)) {
                continue;
            }

            // 过滤系统字段
            if (crmObjectApiNameEnum == CrmObjectApiNameEnum.MEMBER){
                if("system".equals(fieldDescribe.getDefineType()) && !"name".equals(fieldDescribe.getApiName())){
                    continue;
                }
                ImmutableSet<String> excludeFields = ImmutableSet.of("lock_rule", "extend_obj_data_id", "life_status_before_invalid", "owner_department", "integral_value", "area_location", "lock_status",
                        "growth_value", "relevant_team", "lock_user", "grade_id", "owner", "life_status", "record_type", "customer_id", "location");
                if (excludeFields.contains(fieldDescribe.getApiName())){
                    continue;
                }
            }

            // 去除作废字段
            if (fieldDescribe.getIsActive() != null && !fieldDescribe.getIsActive()) {
                continue;
            }

            // 过滤无法处理字段
            if (StringUtils.isNotBlank(fieldDescribe.getApiName()) && CrmV2FieldTypeEnum.IllegalApiName(fieldDescribe.getApiName())) {
                continue;
            }

            //过滤预设字段
            String defineType = (String) fieldDescribe.get("define_type");
            if (StringUtils.isBlank(defineType) || !defineType.equals("custom")) {
                continue;
            }

            CrmUserDefineFieldVo crmUserDefineFieldVo = fieldDescribeToDefineVo(fieldDescribe);
            if (crmUserDefineFieldVo.getFieldType() != null) {
                if (crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.Text.getNumber()) || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.LongText.getNumber())
                        || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.Number1.getNumber()) || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.Number2.getNumber())
                        || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.SelectOne.getNumber()) || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.SelectMany.getNumber())
                || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.PhoneNumber.getNumber()) || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.Email.getNumber())
                || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.DateTime.getNumber()) || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.Date1.getNumber())
                || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.Date2.getNumber()) || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.Country.getNumber())
                || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.Province.getNumber()) || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.City.getNumber())
                || crmUserDefineFieldVo.getFieldType().equals(CrmV2FieldTypeEnum.District.getNumber())) {
                    crmUserDefineFieldVo.setFieldTypeName(fieldDescribe.getType());
                    crmUserDefineFieldVoList.add(crmUserDefineFieldVo);
                }
            }
        }

        return crmUserDefineFieldVoList;
    }

    public CreateLeadResult updateCampaign(String campaignId, String ea, Integer userId, Map<String, Object> params) {
        int maxRetries = 3; // 设置最大重试次数
        int retryInterval = 500; // 设置重试间隔时间（毫秒）
        CreateLeadResult createLeadResult = new CreateLeadResult();
        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                ContactData data = new ContactData();
                data.putAll(params);
                data.put(CrmV2LeadFieldEnum.ID.getNewFieldName(), campaignId);
                ActionEditArg arg = new ActionEditArg();
                arg.setObjectData(data);
                Result<ActionEditResult> result = metadataActionService.edit(createHeaderObj(ea, userId), CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), true, true, arg);
                if (result != null && result.getCode() == Result.SUCCESS_CODE) {
                    createLeadResult.setLeadId((String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName()));
                    createLeadResult.setMessage(result.getMessage());
                    return createLeadResult; // 成功则返回结果
                } else {
                    createLeadResult.setMessage(result != null ? result.getMessage() : I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
                    // 如果返回的不是成功代码，也进行重试
                }
            } catch (Exception e) {
                log.warn("CrmV2Manager.updateCampaign error e", e);
                createLeadResult.setMessage(e.getMessage());
            }
            // 如果不是最后一次重试，等待一段时间后重试
            if (attempt < maxRetries - 1) {
                try {
                    Thread.sleep(retryInterval);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("Thread interrupted", ie);
                    break; // 如果线程被中断，则退出循环
                }
            }
        }
        // 如果所有重试都失败，返回最后一次的结果
        return createLeadResult;
    }

    public Result<ActionAddResult> addWechatFriendsRecordObj(String ea, Map<String, Object> dataMap, Integer fsUserId) {
        if (fsUserId == null) {
            fsUserId = -10000;
        }
        ObjectData data = new ObjectData();
        data.setOwner(fsUserId);
        data.putAll(dataMap);
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        return metadataActionService.add(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), false, arg);
    }

    public Result<ActionEditResult> editWechatFriendsRecordObj(String ea, Map<String, Object> dataMap) {
        ObjectData data = new ObjectData();
        data.putAll(dataMap);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(data);
        HeaderObj headerObj = createHeaderObj(ea, -10000);
        return metadataActionService.edit(headerObj, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), true, true, arg);
    }

    public Result<ActionChangeOwnerResult> editWechaGroupObjOwner(String ea, String objectId, Integer ownerId){
        ActionChangeOwnerArg arg = new ActionChangeOwnerArg(objectId, ownerId);
        arg.setOldOwnerStrategy("1");
        Result<ActionChangeOwnerResult> result = metadataActionService.changeOwner(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), arg);
        return result;
    }

    public ObjectData isExistWechatFriendsRecordObj(String ea, Integer fsUserId, Map<String, String> paramMap) {
        List<Filter> filters = new ArrayList<>();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            Filter filter = new Filter();
            filter.setFieldName(entry.getKey());
            filter.setOperator(Filter.OperatorContants.EQ);
            filter.setFieldValues(ImmutableList.of(entry.getValue()));
            filters.add(filter);
        }
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(100);
        searchQuery.setFilters(filters);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> pageResult = metadataControllerService.list(createHeaderObj(ea, fsUserId), CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), controllerListArg);
        if (pageResult != null && pageResult.isSuccess() && pageResult.getData() != null && !CollectionUtils.isEmpty(pageResult.getData().getDataList())) {
            return pageResult.getData().getDataList().get(0);
        }
        return null;
    }

    public Result<ActionEditResult> editWechatExternalUserObj(String ea, Map<String, Object> dataMap, Integer fsUserId) {
        if (fsUserId == null) {
            fsUserId = -10000;
        }
        ObjectData data = new ObjectData();
        data.put(CampaignMembersObjApiNameEnum.OWNER.getApiName(), Lists.newArrayList(fsUserId.toString()));
        data.putAll(dataMap);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(data);
        HeaderObj headerObj = createHeaderObj(ea, fsUserId);
        return metadataActionService.edit(headerObj, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), true, true, arg);
    }

    /**
     * 查询对象是否存在某个字段
     */
    @FilterLog
    public boolean isExistFiled(String ea,CrmObjectApiNameEnum crmObjectApiNameEnum,String filed) {
        try {
            // 1.获取对象描述
            List<CrmUserDefineFieldVo> fieldVos = this.getAllObjectFieldDescribesList(ea, crmObjectApiNameEnum);
            List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(crmFieldVOS)) {
                return false;
            }
            List<String> apiNames = crmFieldVOS.stream().map(CrmFieldResult::getFieldName).collect(Collectors.toList());
            // 2.添加 判断是否包含wx_union_id
            if (!apiNames.contains(filed)) {
                log.info("CrmV2Manager.isExistFiled not exist field ea:{} field:{}", ea, filed);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.warn("isExistFiled is error");
            return false;
        }
    }

    public Result<ActionAddResult> addWechatFanObj(String ea, Map<String, Object> dataMap) {
        ObjectData data = new ObjectData();
        data.put(CampaignMembersObjApiNameEnum.OWNER.getApiName(), Lists.newArrayList(String.valueOf(-10000)));
        data.putAll(dataMap);
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        return metadataActionService.add(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.WECHAT.getName(), false, arg);
    }

    public Result<ActionEditResult> editWechatFanObj(String ea, Map<String, Object> dataMap) {
        ObjectData data = new ObjectData();
        data.put(CampaignMembersObjApiNameEnum.OWNER.getApiName(), Lists.newArrayList(String.valueOf(-10000)));
        data.putAll(dataMap);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(data);
        return metadataActionService.edit(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.WECHAT.getName(), true, true, arg);
    }

    public ObjectData getWechatFanObjByOpenId(String ea, String wxappid, String openId) {
        List<Filter> filterList = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFieldName("wx_app_id");
        filter.setOperator(OperatorConstants.EQ);
        filter.setFieldValues(Collections.singletonList(wxappid));
        filterList.add(filter);
        Filter filter1 = new Filter();
        filter1.setFieldName("wx_open_id");
        filter1.setOperator(OperatorConstants.EQ);
        filter1.setFieldValues(Collections.singletonList(openId));
        filterList.add(filter1);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT.getName());
        controllerListArg.setIncludeLayout(false);
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setIncludeButtonInfo(false);
        SearchQuery searchQuery = new SearchQuery();
        filterList.forEach(data -> searchQuery.addFilter(data.getFieldName(), data.getFieldValues(), data.getOperator()));
        searchQuery.setOffset(0);
        searchQuery.setLimit(1);
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> result = metadataControllerService.list(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.WECHAT.getName(), controllerListArg);
        if (result.isSuccess() && result.getData() != null && result.getData().getDataList() != null && !result.getData().getDataList().isEmpty()) {
            return result.getData().getDataList().get(0);
        }
        return null;
    }

    /** 较为通用的对象数据操作方法 **/
    public ObjectData isExistObjectData(String ea, String apiName, Map<String, String> paramMap) {
        List<Filter> filters = new ArrayList<>();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            if (entry.getValue() == null) continue;
            Filter filter = new Filter();
            filter.setFieldName(entry.getKey());
            filter.setOperator(Filter.OperatorContants.EQ);
            filter.setFieldValues(ImmutableList.of(entry.getValue()));
            filters.add(filter);
        }
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1);
        searchQuery.setFilters(filters);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> pageResult = metadataControllerService.list(createHeaderObj(ea, -10000), apiName, controllerListArg);
        if (pageResult != null && pageResult.isSuccess() && pageResult.getData() != null && !CollectionUtils.isEmpty(pageResult.getData().getDataList())) {
            return pageResult.getData().getDataList().get(0);
        }
        return null;
    }

    public ObjectData queryObjectData(String ea, String apiName, Map<String, String> paramMap) {
        List<Filter> filters = new ArrayList<>();
        try {
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                if (entry.getValue() == null) continue;
                Filter filter = new Filter();
                filter.setFieldName(entry.getKey());
                filter.setOperator(Filter.OperatorContants.EQ);
                if (entry.getValue() == null) {
                    continue;
                }
                filter.setFieldValues(ImmutableList.of(entry.getValue()));
                filters.add(filter);
            }
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setLimit(1);
            searchQuery.setFilters(filters);
            searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
            ControllerListArg controllerListArg = new ControllerListArg();
            controllerListArg.setIncludeDescribe(false);
            controllerListArg.setSearchQuery(searchQuery);
            Result<Page<ObjectData>> pageResult = metadataControllerService.list(createHeaderObj(ea, -10000), apiName, controllerListArg);
            if (pageResult != null && pageResult.isSuccess() && pageResult.getData() != null && !CollectionUtils.isEmpty(pageResult.getData().getDataList())) {
                return pageResult.getData().getDataList().get(0);
            }
        } catch (Exception e) {
            log.warn("CrmV2Manager queryObjectData error", e);
        }
        return null;
    }

    public List<ObjectData> queryObjectDatas(String ea, String apiName, Map<String, Object> paramMap) {
        return queryObjectDatas(ea, apiName, paramMap, 1000);
    }

    public List<ObjectData> queryObjectDatas(String ea, String apiName, Map<String, Object> paramMap, Integer limit) {
        List<Filter> filters = new ArrayList<>();
        try {
            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                if (entry.getValue() == null) continue;
                Filter filter = new Filter();
                filter.setFieldName(entry.getKey());
                if (entry.getValue() == null) {
                    continue;
                }
                if (entry.getValue() instanceof List) {
                    filter.setFieldValues((List<String>) entry.getValue());
                    filter.setOperator(Filter.OperatorContants.IN);
                } else {
                    filter.setFieldValues(ImmutableList.of(entry.getValue().toString()));
                    filter.setOperator(Filter.OperatorContants.EQ);
                }
                filters.add(filter);
            }
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setLimit(limit == null ? 1000 : limit);
            searchQuery.setFilters(filters);
            searchQuery.setSearchSource(PaasSearchSourceEnum.ES.getValue());
            searchQuery.setOrders(Lists.newArrayList(new OrderBy("create_time", false)));
            ControllerListArg controllerListArg = new ControllerListArg();
            controllerListArg.setIncludeDescribe(false);
            controllerListArg.setSearchQuery(searchQuery);
            controllerListArg.setSearchRichTextExtra(true);
            Result<Page<ObjectData>> pageResult = metadataControllerService.list(createHeaderObj(ea, -10000), apiName, controllerListArg);
            if (pageResult != null && pageResult.isSuccess() && pageResult.getData() != null && !CollectionUtils.isEmpty(pageResult.getData().getDataList())) {
                return pageResult.getData().getDataList();
            }
        } catch (Exception e) {
            log.warn("CrmV2Manager queryObjectDatas error", e);
        }
        return Lists.newArrayList();
    }

    /**
     * 新增对象
     *
     * @param ea
     * @param apiName
     * @param owner
     * @param dataMap
     * @return
     */
    public Result<ActionAddResult> addObjectData(String ea, String apiName, Integer owner, Map<String, Object> dataMap) {
        ObjectData data = new ObjectData();
        data.setOwner(owner != null ? owner : null);
        data.putAll(dataMap);
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        arg.setSkipCheckCleanOwner(true);
        ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
        optionInfo.setCalculateDefaultValue(true);
        arg.setOptionInfo(optionInfo);
        return metadataActionService.add(createHeaderObj(ea, -10000), apiName, false, arg);
    }

    /**
     * 编辑对象
     *
     * @param ea
     * @param apiName
     * @param dataMap
     * @return
     */
    public Result<ActionEditResult> editObjectData(String ea, String apiName, Map<String, Object> dataMap) {
        ObjectData data = new ObjectData();
        data.putAll(dataMap);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(data);
        HeaderObj headerObj = createHeaderObj(ea, -10000);
        return metadataActionService.edit(headerObj, apiName, true, true, arg);
    }

    public Result<ActionEditResult> editObjectData(String ea, String apiName, Map<String, Object> dataMap, boolean triggerWorkFlow, boolean triggerFlow) {
        ObjectData data = new ObjectData();
        data.putAll(dataMap);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(data);
        HeaderObj headerObj = createHeaderObj(ea, SuperUserConstants.USER_ID);
        return metadataActionService.edit(headerObj, apiName, triggerWorkFlow, triggerFlow, arg);
    }

    public Result<ActionEditResult> editWithNotValidateParam(String ea, String apiName, Map<String, Object> dataMap, boolean triggerWorkFlow, boolean triggerFlow, boolean notValidate) {
        ObjectData data = new ObjectData();
        data.putAll(dataMap);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(data);
        HeaderObj headerObj = createHeaderObj(ea, SuperUserConstants.USER_ID);
        return metadataActionService.editWithNotValidateParam(headerObj, apiName, triggerWorkFlow, triggerFlow, notValidate, arg);
    }

    /**
     * 正常情况下更新负责人应该同步更新归属组织, 平台bug不再业务处理
     *
     * @param ea
     * @param apiName
     * @param id
     * @param owner
     */
    @Deprecated
    public void editObjectDataOwnOrganization(String ea, String apiName, String id, Integer owner) {
        /*
        try {
            if (StringUtils.isEmpty(id) || owner == null || owner == -10000) {
                return;
            }
            ObjectData objectData = this.getOneByList(ea, -10000, apiName, id);
            if (objectData == null) {
                return;
            }
            HashMap<String, String> paramMap = new HashMap<>();
            paramMap.put("user_id", String.valueOf(owner));
            ObjectData personnelObj = this.queryObjectData(ea, "PersonnelObj", paramMap);
            if (personnelObj == null) {
                return;
            }
            Object data_own_organization = personnelObj.get("data_own_organization");
            if (data_own_organization == null) {
                return;
            }
            List<String> dataOwnOrganization = (List) data_own_organization;
            if (CollectionUtils.isEmpty(dataOwnOrganization)) {
                return;
            }
            if (objectData.get("data_own_organization") != null && ListUtils.isEqualList(dataOwnOrganization, (List) objectData.get("data_own_organization"))) {
                return;
            }
            ObjectData data = new ObjectData();
            data.put("_id", id);
            data.put("data_own_organization", dataOwnOrganization);
            ActionEditArg arg = new ActionEditArg();
            arg.setObjectData(data);
            HeaderObj headerObj = createHeaderObj(ea, SuperUserConstants.USER_ID);
            metadataActionService.edit(headerObj, apiName, false, false, arg);
        } catch (Exception e) {
            log.warn("editObjectDataOwnOrganization error", e);
        }
        */
    }

    /**
     * 修改对象负责人
     *
     * @param ea
     * @param apiName
     * @param id
     * @param owner
     */
    public void changeObjectOwner(String ea, String apiName, String id, Integer owner) {
        HeaderObj headerObj = createHeaderObj(ea, -10000);
        metadataActionService.changeOwner(headerObj, apiName, new ActionChangeOwnerArg(id, owner));
    }

    /**
     * 根据参数查询判断是否存在,
     * 不存在:新增
     * 存在:编辑并修改负责人(负责人值未变不会发生更新)
     *
     * @param ea
     * @param apiName
     * @param owner
     * @param paramMap
     * @param dataMap
     * @return
     */
    public Result addOrUpdateObjectDataAndChangOwner(String ea, String apiName, Integer owner, Map<String, String> paramMap, Map<String, Object> dataMap) {
        ObjectData existObjectData = this.isExistObjectData(ea, apiName, paramMap);
        if (existObjectData != null) {
            dataMap.put("_id", existObjectData.getId());
            this.changeObjectOwner(ea, apiName, existObjectData.getId(), owner);
            return this.editObjectData(ea, apiName, dataMap);
        } else {
            return this.addObjectData(ea, apiName, owner, dataMap);
        }
    }


    /**
     * 根据对象筛选选择器查询对象ID
     *
     * @param ea
     * @param ruleGroupJson
     * @return
     */
    public List<String> getObjIdsByRuleGroupJson(String ea, RuleGroupList ruleGroupJson) {
        if (CollectionUtils.isEmpty(ruleGroupJson)) {
            return null;
        }
        List<PaasQueryFilterArg> args = ruleGroupJson.stream().map(val -> BeanUtil.copyByGson(val, PaasQueryFilterArg.class)).collect(Collectors.toList());
        PaasQueryFilterArg paasQueryFilterArg = args.get(0);
        paasQueryFilterArg.setSelectFields(Collections.singletonList("_id"));
        paasQueryFilterArg.getQuery().getFilters().forEach(e -> {
            if ("tag".equals(e.getFieldName())) {
                e.setValueType(11);
                e.setOperator("IN".equals(e.getOperator()) ? "LIKE" : e.getOperator());
            }
        });
        InnerPage<ObjectData> objectDataInnerPage = this.concurrentListCrmObjectByFilterV3(ea, -10000, paasQueryFilterArg);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
            return objectDataInnerPage.getDataList().stream().map(data -> data.getString("_id")).distinct().collect(Collectors.toList());
        }
        return null;
    }



    public Result<ActionEditResult> updateObjs(String ea, String apiName, Map<String, Object> dataMap){
        ObjectData data = new ObjectData();
        data.putAll(dataMap);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(data);
        HeaderObj headerObj = createHeaderObj(ea, -10000);
        Result<ActionEditResult> edit = metadataActionService.edit(headerObj, apiName, true, true, arg);
        return edit;
    }

    // 获取对象的归属组织
    public String getObjectDataOwnOrganization(String ea, String apiName, String id) {
        String dataOwnOrganization = null;
        try {
            ObjectData objectData = getDetail(ea, SuperUserConstants.USER_ID, apiName, id);
            if (objectData == null) {
                return null;
            }
            Object dataOwnOrganizationObj = objectData.get(CustomizeFormDataConstants.CRM_OWN_ORGANIZATION);
            if (dataOwnOrganizationObj != null) {
                List<Object> dataOwnOrganizationObjList;
                if (dataOwnOrganizationObj instanceof List && (dataOwnOrganizationObjList = (List)dataOwnOrganizationObj).size() > 0) {
                    dataOwnOrganization = dataOwnOrganizationObjList.get(0).toString();
                } else {
                    dataOwnOrganization = dataOwnOrganizationObj.toString();
                }
            }
        } catch (Exception e) {
            log.error("获取对象的归属组织异常, ea: {} apiNmae: {} id: {}", ea, apiName, id, e);
        }
        return dataOwnOrganization;
    }

    /**
     * 获取企业所有对象的api_name 和display_name
     * @param ea
     * @return
     */
    public Map<String,String> getAllObjDescribeApiNameAndDisplayName(String ea) {
        Map<String,String> map = Maps.newHashMap();
        com.fxiaoke.crmrestapi.common.result.Result<List<ObjectaDescribeAllData>> crmResult = objectDescribeService.all(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000));
        if (crmResult == null || !crmResult.isSuccess()){
            return map;
        }
        List<String> userMarketingApiNames = Lists.newArrayList("CampaignMembersObj", "LeadsObj","MemberObj");
        for (ObjectaDescribeAllData describeData : crmResult.getData()){
            if (!userMarketingApiNames.contains(describeData.getDescribeApiName())) {
                map.put(describeData.getDescribeApiName(),describeData.getDescribeDisplayName());
            }
        }
        return map;
    }

    /**
     * 判断对象是否存在
     * @param ea
     * @param crmObjectApiName
     * @return
     */
    public boolean isExistObject(String ea, String crmObjectApiName) {
        try {
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult =
                    objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), crmObjectApiName);
            return getDescribeResultResult.isSuccess() && getDescribeResultResult.getData() != null && getDescribeResultResult.getData().getDescribe() != null;
        } catch (Exception e) {
            log.warn("CrmV2Manager isExistObject error", e);
        }
        return false;
    }

    public Result<ActionBulkDeleteResult> bulkDelete(String ea, String apiName, List<String> deleteIdList) {
        if (CollectionUtils.isEmpty(deleteIdList)) {
            Result<ActionBulkDeleteResult> result = new Result<>();
            result.setCode(Result.SUCCESS_CODE);
            result.setMessage("OK");
            return result;
        }
        ActionBulkDeleteArg arg = new ActionBulkDeleteArg();
        arg.setIdList(deleteIdList);
        arg.setDescribe_api_name(apiName);
        HeaderObj headerObj = createHeaderObj(ea, -10000);
        return metadataActionService.bulkDelete(headerObj, apiName, arg);
    }

    public Map<String, Object> buildHeader(String ea, int fsUserId) {
        return buildHeader(eieaConverter.enterpriseAccountToId(ea), fsUserId);
    }

    public Map<String, Object> buildHeader(int ei, int fsUserId) {
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("x-fs-ei", String.valueOf(ei));
        headerMap.put("x-fs-userInfo", String.valueOf(fsUserId));
        headerMap.put("x-tenant-id", String.valueOf(ei));
        return headerMap;
    }

    public List<ObjectData> getAllObjByQueryArg(String ea, String objectAPIName, List<String> selectFields, PaasQueryArg paasQueryArg) {
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(objectAPIName);
        queryFilterArg.setSelectFields(selectFields);
        queryFilterArg.setQuery(paasQueryArg);
        List<ObjectData> resultList = Lists.newArrayList();
        int totalCount = this.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        if (totalCount <= 0) {
            return resultList;
        }
        int count = 0;
        String lastId = null;
        while (count < totalCount) {
            InnerPage<ObjectData> objectDataInnerPage = this.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, 500);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
            resultList.addAll(objectDataList);
            count += objectDataList.size();
            lastId = objectDataList.get(objectDataList.size() - 1).getId();
        }
        return resultList;
    }

    /**
     * 更新市场活动活动落地页
     * @param ea
     * @param evenId
     * @param fsUserId
     * @param landingPage
     * @return
     */
    public Result<ActionEditResult> updateMarketingEvenObjLandingPage(String ea,String evenId, Integer fsUserId,String landingPage) {
        if (fsUserId == null) {
            fsUserId = -10000;
        }
        ActionEditArg arg = new ActionEditArg();
        ObjectData data = new ObjectData();
        data.put(CrmV2LeadFieldEnum.ID.getNewFieldName(),evenId);
        data.put("event_landing_page",landingPage);
        arg.setObjectData(data);
        Result<ActionEditResult> result =  metadataActionService.edit(createHeaderObj(ea, fsUserId), CrmObjectApiNameEnum.MARKETING_EVENT.getName(),true,true,arg);
        return result;
    }

    public Result<ActionAddResult> addWhatsAppSendRecordObj(String ea, Map<String, Object> dataMap, Integer fsUserId) {
        if (fsUserId == null) {
            fsUserId = -10000;
        }
        ObjectData data = new ObjectData();
        data.setOwner(fsUserId);
        data.putAll(dataMap);
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        return metadataActionService.add(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.WHATSAPP_SEND_RECORD_OBJ.getName(), false, arg);
    }

    public Result<ActionEditResult> editWhatsAppSendRecordObj(String ea, Map<String, Object> dataMap) {
        ObjectData data = new ObjectData();
        data.putAll(dataMap);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(data);
        HeaderObj headerObj = createHeaderObj(ea, -10000);
        return metadataActionService.edit(headerObj, CrmObjectApiNameEnum.WHATSAPP_SEND_RECORD_OBJ.getName(), true, true, arg);
    }

    public Result<AggregateQueryResult> aggregateQuery(String ea, String objectApiName, AggregateParameterArg aggregateParameterArg) {
        AggregateQueryArg aggregateQueryArg = new AggregateQueryArg();
        aggregateQueryArg.setDescribeApiName(objectApiName);
        aggregateQueryArg.setSearchQueryInfo(JsonUtil.toJson(aggregateParameterArg));
        int ei = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj headerObj = HeaderObj.newInstance(ei, SuperUserConstants.USER_ID);
        Result<AggregateQueryResult> result = objectDataServiceV3.aggregateQuery(headerObj, aggregateQueryArg);
        log.info("aggregateQuery ea: {} arg: {} result:{}", ea, aggregateParameterArg, result);
        return result;
    }

    public Result<ActionBulkCreateResult> bulkCreate(String ea, Integer fsUserId, String apiName, List<ObjectData> objectDataList, boolean useValidationRule, boolean isDuplicateSearch, boolean skipFuncValidate) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            Result<ActionBulkCreateResult> result = new Result<>();
            result.setCode(0);
            return result;
        }
        HeaderObj headerObj = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId);
        ActionBulkCreateArg actionBulkCreateArg = new ActionBulkCreateArg();
        for (ObjectData objectData : objectDataList) {
            objectData.put("object_describe_api_name", apiName);
//            objectData.put("object_describe_id", apiName);  /*mt_data通表object_describe_id长度为24如果apiName长度大于24插入通表会报错*/
            objectData.put("object_describe_id", "");
        }
        actionBulkCreateArg.setDataList(objectDataList);
        ActionBulkCreateArg.OptionInfo optionInfo = new ActionBulkCreateArg.OptionInfo();
        optionInfo.setIsDuplicateSearch(isDuplicateSearch);
        optionInfo.setSkipFuncValidate(skipFuncValidate);
        optionInfo.setUseValidationRule(useValidationRule);
        actionBulkCreateArg.setOptionInfo(optionInfo);
        return metadataActionService.bulkCreate(headerObj, apiName, actionBulkCreateArg);

    }
    public com.facishare.marketing.common.result.Result<GetRelatedDataListResult> findDescribField(String ea, Integer fsUserId, boolean isSkipMarketingObjectDisplayName) {
        com.fxiaoke.crmrestapi.common.result.Result<List<ObjectaDescribeAllData>> crmResult = objectDescribeService.all(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000));
        if (crmResult == null || !crmResult.isSuccess()){
            return com.facishare.marketing.common.result.Result.newSuccess();
        }

        List<String> userMarketingApiNames = Lists.newArrayList("EnterpriseInfoObj", "LeadsObj", "AccountObj", "ContactObj", "WechatWorkExternalUserObj", "WechatFanObj", "MemberObj");
        GetRelatedDataListResult result = new GetRelatedDataListResult();
        List<Map<String, Object>> resultList = Lists.newArrayList();
        result.setResultList(resultList);
        for (ObjectaDescribeAllData describeData : crmResult.getData()){
            Map<String, Object> map = new HashMap<>();
            if (isSkipMarketingObjectDisplayName && !userMarketingApiNames.contains(describeData.getDescribeApiName())) {
                map.put("api_name", describeData.getDescribeApiName());
                map.put("display_name", describeData.getDescribeDisplayName());
                resultList.add(map);
            }else if (!isSkipMarketingObjectDisplayName){
                map.put("api_name", describeData.getDescribeApiName());
                map.put("display_name", describeData.getDescribeDisplayName());
                resultList.add(map);
            }
        }

        return com.facishare.marketing.common.result.Result.newSuccess(result);
    }


    /**
     * 根据参数查询判断是否存在,
     * 不存在:新增
     * 存在:编辑并修改负责人(负责人值未变不会发生更新)
     *
     * @param ea
     * @param apiName
     * @param owner
     * @param paramMap
     * @param dataMap
     * @return
     */
    public Result addOrUpdateObjectDataAndAssetData(String ea, String apiName, Integer owner, Map<String, String> paramMap, Map<String, Object> dataMap) {
        ObjectData existObjectData = this.isExistObjectData(ea, apiName, paramMap);
        if (existObjectData != null) {
            Map<String, Object> updateDataMap = new HashMap<>();
            updateDataMap.put("_id", existObjectData.getId());
            updateDataMap.put("asset_data",dataMap.get("asset_data"));
            return this.editObjectData(ea, apiName, updateDataMap);
        } else {
            return this.addObjectData(ea, apiName, owner, dataMap);
        }
    }
}