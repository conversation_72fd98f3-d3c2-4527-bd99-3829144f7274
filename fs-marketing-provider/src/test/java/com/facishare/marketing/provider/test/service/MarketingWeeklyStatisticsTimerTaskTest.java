package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.result.WeekStatisticsResult;
import com.facishare.marketing.api.service.ActivityService;
import com.facishare.marketing.api.service.EnterpriseEmployeeObjectDayStatisticService;
import com.facishare.marketing.api.service.EnterpriseMetaConfigService;
import com.facishare.marketing.api.service.EnterpriseObjectStatisticService;
import com.facishare.marketing.api.service.EnterpriseStatisticService;
import com.facishare.marketing.api.service.MarketingUserGroupService;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityExternalConfigService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.thread.AsyncBatchTimerTaskExecutor;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.test.BaseTest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: dongzhb
 * @date: 2019/3/21
 * @Description:
 */
@Slf4j

public class MarketingWeeklyStatisticsTimerTaskTest extends BaseTest {
    protected final static ExecutorService THREAD_POOL = new AsyncBatchTimerTaskExecutor();
    @Autowired
    private EnterpriseMetaConfigService enterpriseMetaConfigService;
    @Autowired
    private EnterpriseObjectStatisticService enterpriseObjectStatisticService;
    @Autowired
    private EnterpriseStatisticService enterpriseStatisticService;
    @Autowired
    private MarketingUserGroupService marketingUserGroupService;
    @Autowired
    private EnterpriseEmployeeObjectDayStatisticService enterpriseEmployeeObjectDayStatisticService;
    @Autowired
    private MarketingActivityExternalConfigService marketingActivityExternalConfigService;
    @Autowired
    private ActivityService activityService;

    @Test
    public void handleWeekStatisticsTimerTask() {
        Date startDate = DateUtil.getLastWeekMonday();
        Date endDate = DateUtil.getLastWeekWeekday();
        long beginTime = System.currentTimeMillis();
        log.info("handleWeekStatisticsTimerTask beginTime execute ={}", beginTime);
        log.info("handleWeekStatisticsTimerTask  query   startDate ={} , endDate ={}", startDate, endDate);
        //开通企业数
        enterpriseMetaConfigService.getEnterpriseIsOpenWeeklyStatistics(startDate, endDate);
        //活跃企业数
        enterpriseEmployeeObjectDayStatisticService.getActiveNumberEnterprisesWeeklyStatistics(startDate, endDate);
        //统计推广活动
        marketingActivityExternalConfigService.getMarketingActivityWeeklyStatistics(startDate, endDate);
        //企业员工推广次数
        enterpriseEmployeeObjectDayStatisticService.getSpreadCountEnterpriseEmployeeObjectDayWeeklyStatistics(startDate, endDate);
        //企业员工推广人数
        enterpriseEmployeeObjectDayStatisticService.getForwardCountEnterpriseEmployeeObjectDayWeeklyStatistics(startDate, endDate);
        //新增营销物料数
        Result<Integer> result = activityService.getMaterialsWeeklyStatistics(startDate, endDate);
        //所有物料的访客pv
        enterpriseObjectStatisticService.getEnterpriseObjectDayWeeklyStatistics(startDate, endDate);
        //获取线索数
        enterpriseStatisticService.getEnterpriseEmployeeLeadsWeeklyStatistics(startDate, endDate);
        //营销用户数
        marketingUserGroupService.getUserMarketingAccountWeeklyStatistics(startDate, endDate);
        long endTime = System.currentTimeMillis();
        log.info("handleWeekStatisticsTimerTask endTime execute ={}", endTime - beginTime);
    }

    @Test
    public void handleAsyWeekStatisticsTimerTask() {
        Date startDate = DateUtil.getLastWeekMonday();
        Date endDate = DateUtil.getLastWeekWeekday();
        long beginTime = System.currentTimeMillis();
        log.info("handleWeekStatisticsTimerTask beginTime execute ={}", beginTime);
        WeekStatisticsResult weekStatisticsResult = new WeekStatisticsResult();
        weekStatisticsResult.setStartDate(DateUtil.getTimeStamp(startDate));
        weekStatisticsResult.setEndDate(DateUtil.getTimeStamp(endDate));
        List<CompletableFuture> futures = new ArrayList<>();
        //企业开通数
        doGetAllEnterpriseIsOpenWeeklyStatistics(futures, startDate, endDate, weekStatisticsResult);
        ////活跃企业数
        doGetAllActiveNumberEnterprisesWeeklyStatistics(futures, startDate, endDate, weekStatisticsResult);
        ////统计推广活动
        doGetAllMarketingActivityWeeklyStatistics(futures, startDate, endDate, weekStatisticsResult);
        ////企业员工推广次数
        doGetAllSpreadCountEnterpriseEmployeeObjectDayWeeklyStatistics(futures, startDate, endDate, weekStatisticsResult);
        ////企业员工推广人数
        doGetAllForwardCountEnterpriseEmployeeObjectDayWeeklyStatistics(futures, startDate, endDate, weekStatisticsResult);
        ////新增营销物料数
        doGetAllMaterialsWeeklyStatistics(futures, startDate, endDate, weekStatisticsResult);
        ////所有物料的访客pv
        doGetAllEnterpriseObjectDayWeeklyStatistics(futures, startDate, endDate, weekStatisticsResult);
        ////获取线索数
        doGetAllEnterpriseEmployeeLeadsWeeklyStatistics(futures, startDate, endDate, weekStatisticsResult);
        //  //营销用户数
        doGetAllUserMarketingAccountWeeklyStatistics(futures, startDate, endDate, weekStatisticsResult);

        CompletableFuture allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));
        try {
            allDoneFuture.get();
            long endTime = System.currentTimeMillis();
            log.info("handleWeekStatisticsTimerTask execute totalTime={} , WeekStatisticsResult={}", endTime - beginTime, weekStatisticsResult);
        } catch (Exception e) {
            log.error("handleWeekStatisticsTimerTask error :", e);
        }
    }

    //新开通企业数
    private void doGetAllEnterpriseIsOpenWeeklyStatistics(List<CompletableFuture> futures, Date startDate, Date endDate, WeekStatisticsResult weekStatisticsResult) {
        futures.add(CompletableFuture.runAsync(() -> {
            //开通企业数
            try {
                Result<Integer> result = enterpriseMetaConfigService.getEnterpriseIsOpenWeeklyStatistics(startDate, endDate);
                weekStatisticsResult.setEnterpriseIsOpenCount(result.getData());
            } catch (Exception e) {
                log.error("handleWeekStatisticsTimerTask getEnterpriseIsOpenWeeklyStatistics error :", e);
            }
        }, THREAD_POOL));
    }

    //活跃企业数
    private void doGetAllActiveNumberEnterprisesWeeklyStatistics(List<CompletableFuture> futures, Date startDate, Date endDate, WeekStatisticsResult weekStatisticsResult) {
        futures.add(CompletableFuture.runAsync(() -> {
            //活跃企业数
            try {
                Result<Integer> result = enterpriseEmployeeObjectDayStatisticService.getActiveNumberEnterprisesWeeklyStatistics(startDate, endDate);
                weekStatisticsResult.setEnterpriseActiveCount(result.getData());
            } catch (Exception e) {
                log.error("handleWeekStatisticsTimerTask getActiveNumberEnterprisesWeeklyStatistics error :", e);
            }
        }, THREAD_POOL));
    }

    //统计推广活动
    private void doGetAllMarketingActivityWeeklyStatistics(List<CompletableFuture> futures, Date startDate, Date endDate, WeekStatisticsResult weekStatisticsResult) {
        futures.add(CompletableFuture.runAsync(() -> {
            try {
                //统计推广活动
                Result<Integer> result = marketingActivityExternalConfigService.getMarketingActivityWeeklyStatistics(startDate, endDate);
                weekStatisticsResult.setEnterpriseSpreadCount(result.getData());
            } catch (Exception e) {
                log.error("handleWeekStatisticsTimerTask getMarketingActivityWeeklyStatistics error :", e);
            }
        }, THREAD_POOL));
    }

    private void doGetAllSpreadCountEnterpriseEmployeeObjectDayWeeklyStatistics(List<CompletableFuture> futures, Date startDate, Date endDate, WeekStatisticsResult weekStatisticsResult) {
        futures.add(CompletableFuture.runAsync(() -> {
            //企业员工推广次数
            try {
                Result<Integer> result = enterpriseEmployeeObjectDayStatisticService.getSpreadCountEnterpriseEmployeeObjectDayWeeklyStatistics(startDate, endDate);
                weekStatisticsResult.setEnterpriseEmployeeSpreadCount(result.getData());
            } catch (Exception e) {
                log.error("handleWeekStatisticsTimerTask getSpreadCountEnterpriseEmployeeObjectDayWeeklyStatistics error :", e);
            }
        }, THREAD_POOL));
    }

    //企业员工推广人数
    private void doGetAllForwardCountEnterpriseEmployeeObjectDayWeeklyStatistics(List<CompletableFuture> futures, Date startDate, Date endDate, WeekStatisticsResult weekStatisticsResult) {
        futures.add(CompletableFuture.runAsync(() -> {
            try {
                //企业员工推广人数
                Result<Integer> result = enterpriseEmployeeObjectDayStatisticService.getForwardCountEnterpriseEmployeeObjectDayWeeklyStatistics(startDate, endDate);
                weekStatisticsResult.setEnterpriseForwardCount(result.getData());
            } catch (Exception e) {
                log.error("handleWeekStatisticsTimerTask getForwardCountEnterpriseEmployeeObjectDayWeeklyStatistics error :", e);
            }
        }, THREAD_POOL));
    }

    //新增营销物料数
    private void doGetAllMaterialsWeeklyStatistics(List<CompletableFuture> futures, Date startDate, Date endDate, WeekStatisticsResult weekStatisticsResult) {
        futures.add(CompletableFuture.runAsync(() -> {
            //新增营销物料数
            try {
                Result<Integer> result = activityService.getMaterialsWeeklyStatistics(startDate, endDate);
                weekStatisticsResult.setEnterpriseMaterialsIncreaseCount(result.getData());
            } catch (Exception e) {
                log.error("handleWeekStatisticsTimerTask getMaterialsWeeklyStatistics error :", e);
            }
        }, THREAD_POOL));
    }

    //所有物料的访客pv
    private void doGetAllEnterpriseObjectDayWeeklyStatistics(List<CompletableFuture> futures, Date startDate, Date endDate, WeekStatisticsResult weekStatisticsResult) {
        futures.add(CompletableFuture.runAsync(() -> {
            //所有物料的访客pv
            try {
                Result<Integer> result = enterpriseObjectStatisticService.getEnterpriseObjectDayWeeklyStatistics(startDate, endDate);
                weekStatisticsResult.setEnterpriseMaterialsPvCount(result.getData());
            } catch (Exception e) {
                log.error("handleWeekStatisticsTimerTask getEnterpriseObjectDayWeeklyStatistics error :", e);
            }
        }, THREAD_POOL));
    }

    //获取线索数
    private void doGetAllEnterpriseEmployeeLeadsWeeklyStatistics(List<CompletableFuture> futures, Date startDate, Date endDate, WeekStatisticsResult weekStatisticsResult) {
        futures.add(CompletableFuture.runAsync(() -> {
            //获取线索数
            try {
                Result<Integer> result = enterpriseStatisticService.getEnterpriseEmployeeLeadsWeeklyStatistics(startDate, endDate);
                weekStatisticsResult.setEnterpriseEmployeeLeadsCount(result.getData());
            } catch (Exception e) {
                log.error("handleWeekStatisticsTimerTask getEnterpriseEmployeeLeadsWeeklyStatistics error :", e);
            }
        }, THREAD_POOL));
    }

    //营销用户数
    private void doGetAllUserMarketingAccountWeeklyStatistics(List<CompletableFuture> futures, Date startDate, Date endDate, WeekStatisticsResult weekStatisticsResult) {
        futures.add(CompletableFuture.runAsync(() -> {
            //营销用户数
            try {
                Result<Integer> result = marketingUserGroupService.getUserMarketingAccountWeeklyStatistics(startDate, endDate);
                weekStatisticsResult.setUserMarketingAccountCount(result.getData());
            } catch (Exception e) {
                log.error("handleWeekStatisticsTimerTask getUserMarketingAccountWeeklyStatistics error :", e);
            }
        }, THREAD_POOL));
    }
}
