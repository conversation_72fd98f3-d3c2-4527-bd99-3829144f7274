package com.facishare.marketing.provider.service.connector.douyin;

import com.facishare.marketing.api.arg.connector.DouYinLeadDataArg;
import com.facishare.marketing.api.service.connector.douyin.DouYinService;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class DouYinServiceImplTest  extends BaseTest {
    @Autowired
    private DouYinService douYinService;

    @Test
    public void queryDouYinLeadDataTest() {
        DouYinLeadDataArg arg = new DouYinLeadDataArg();
        arg.setTenantId(88146);
        arg.setApiSecret("57d9abf6963c70c56270497a5270c0c5");
        arg.setApiKey("awhxzw7z2wzq9wo5");
        arg.setAccountId("7362015934192601139");
        arg.setObjAPIName("douyin_lead");
        arg.setStartTime(1739230200000L);
        arg.setEndTime(1739232000000L);
        arg.setOffset(0);
        arg.setLimit(100);
        douYinService.queryDouYinLeadData(arg);
    }
}
