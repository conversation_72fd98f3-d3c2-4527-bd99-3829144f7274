package com.facishare.marketing.provider.manager.marketingAssistant;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.common.typehandlers.value.SendWxTemplateMsgArg;
import com.facishare.marketing.provider.dao.NoticeDAO;
import com.facishare.marketing.provider.entity.NoticeEntity;
import com.facishare.marketing.provider.entity.marketingAssistant.YxzsAllSpreadWxTemplateNoticeSettingEntity;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import com.mysql.jdbc.log.Log;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

@Slf4j
public class YxzsNoticeSendManagerTest extends BaseTest {

    @Autowired
    private YxzsNoticeSendManager yxzsNoticeSendManager;
    @Autowired
    private NoticeDAO noticeDAO;
    @Test
    public void sendUnionMsg() {
        String a = "{\n" +
                "  \"ea\": 88146,\n" +
                "  \"actionTime\": 1722829575640,\n" +
                "  \"channelType\": 3,\n" +
                "  \"uid\": null,\n" +
                "  \"fsUserId\": null,\n" +
                "  \"wxAppId\": null,\n" +
                "  \"wxOpenId\": null,\n" +
                "  \"externalUserid\": null,\n" +
                "  \"fingerPrint\": \"b6184d497d834bec881c0d0b1023f1af\",\n" +
                "  \"actionType\": 64,\n" +
                "  \"objectType\": 26,\n" +
                "  \"objectId\": \"345b0dcc839f4d88bb4763ea185b3c6c\",\n" +
                "  \"marketingActivityId\": null,\n" +
                "  \"spreadFsUid\": null,\n" +
                "  \"phone\": \"***\",\n" +
                "  \"sceneType\": null,\n" +
                "  \"sceneId\": null,\n" +
                "  \"marketingEventId\": null,\n" +
                "  \"sourceType\": null,\n" +
                "  \"partner\": false,\n" +
                "  \"outerTenantId\": null,\n" +
                "  \"outerUid\": null,\n" +
                "  \"upstreamEa\": null,\n" +
                "  \"spreadAdd\": false,\n" +
                "  \"extensionParams\": {\n" +
                "    \"actionDurationTime\": 2044,\n" +
                "    \"actionProportion\": 0,\n" +
                "    \"marketingScene\": 5,\n" +
                "    \"client\": 2,\n" +
                "    \"primaryId\": \"2024080500000115\",\n" +
                "    \"spreadChannel\": \"other\"\n" +
                "  }\n" +
                "}\n";
//        String b = "{\n" +
//                "  \"actionType\": 64,\n" +
//                "  \"objectType\": 26,\n" +
//                "  \"actionTime\": 1722829575640,\n" +
//                "  \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36\",\n" +
//                "  \"objectId\": \"849ae462dad34d53b154217a83eadc57\",\n" +
//                "  \"marketingEventId\": \"66b4ab83d83cb800076b3505\",\n" +
//                "  \"channelValue\": \"sms\",\n" +
//                "  \"channelType\": 3,\n" +
//                "  \"client\": 2,\n" +
//                "  \"ea\": \"88146\",\n" +
//                "  \"referer\": \"https://crm.ceshi112.com/proj/page/marketing-page?marketingEventId=66b4ab83d83cb800076b3505&spreadChannel=sms&objectType=26&ea=88146&id=849ae462dad34d53b154217a83eadc57&type=1\",\n" +
//                "  \"timestamp\": 1723634555669,\n" +
//                "  \"nonce\": \"c01d446035995491\",\n" +
//                "  \"sign\": \"52cbf9be92469c9a87578a7c161edd39\"\n" +
//                "}";
        MarketingUserActionEvent event = JSONObject.parseObject(a, MarketingUserActionEvent.class);
        yxzsNoticeSendManager.sendUnionMsg(event);
        log.info("{}", event);
    }

    @Test
    public void sendAllSpreardWxTemplateMsg() {

        NoticeEntity noticeById = noticeDAO.getNoticeById("35d97400bca8489cb4564c8462dcf121");
        YxzsAllSpreadWxTemplateNoticeSettingEntity entity = new YxzsAllSpreadWxTemplateNoticeSettingEntity();
        entity.setId("7e72cefb24664166aa47d8d21afb7128");
        entity.setEa("88146");
        entity.setOpenWxNotice(1);
        entity.setWxAppId("wx2e8a14a356c46c27");
        SendWxTemplateMsgArg arg = JSONObject.parseObject("{\"msgBody\": {\"title\": \"活动报名成功通知\", \"dataList\": [{\"key\": \"first\", \"title\": \"描述（选填）\"}, {\"key\": \"keyword1\", \"title\": \"活动名称\", \"value\": \"{市场活动}\"}, {\"key\": \"keyword2\", \"title\": \"活动时间\", \"value\": \"{任务时间范围}\"}, {\"key\": \"keyword3\", \"title\": \"活动地点\", \"value\": \"{宣传语}\"}, {\"key\": \"remark\", \"title\": \"备注（选填）\"}]}, \"redirectUrl\": \"\", \"redirectType\": 1, \"wxTemplateMsgId\": \"hTJl9tg9dFTooFeF-MNqhuX4zC0kdv3kJ4JH3QDSEJc\"}", SendWxTemplateMsgArg.class);
        entity.setWxTemplateMsg(arg);
        yxzsNoticeSendManager.sendAllSpreardWxTemplateMsg("title","desc",noticeById,noticeById.getMarketingActivityId(),false,null, Lists.newArrayList(1000),"66b4371dd83cb8000768913b");

    }


}