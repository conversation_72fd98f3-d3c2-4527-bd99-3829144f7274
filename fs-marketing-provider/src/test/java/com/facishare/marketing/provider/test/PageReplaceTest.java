package com.facishare.marketing.provider.test;

import com.facishare.marketing.api.arg.NoticeSendArg;
import com.facishare.marketing.api.arg.PageSopTaskArg;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.service.NoticeService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.manager.marketingactivity.WeChatServiceManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class PageReplaceTest extends BaseTest{

    @Autowired
    private WeChatServiceManager weChatServiceManager;
    @Autowired
    private NoticeService noticeService;

    @Test
    public void test () {
        //创建会议
        String ea = "88164";
        Integer fsUserId = 1000;
        //公众号消息
        AddMarketingActivityArg addMarketingActivityArg = GsonUtil.fromJsonSerializingNull("{\"marketingEventId\":\"65659a68dc62af00011c13e7\",\"materialInfos\":[],\"spreadType\":2,\"weChatTemplateMessageVO\":{\"appId\":\"FSAID_10b07bb8\",\"title\":\"新页面呀\",\"weChatOfficialTemplateId\":\"35bNNOgWBI7Yoz2QokfDjr3sCLOsJLkocrl4z5nBxYU\",\"templateMessageDatas\":{\"title\":\"会议邀请通知\",\"dataList\":[{\"key\":\"first\",\"color\":null,\"title\":\"描述（选填）\"},{\"key\":\"keyword1\",\"color\":null,\"value\":\"张三\",\"title\":\"发起人\"},{\"key\":\"keyword2\",\"color\":null,\"value\":\"张三家\",\"title\":\"会议地点\"},{\"key\":\"keyword3\",\"color\":null,\"value\":\"立刻马上\",\"title\":\"会议时间\"},{\"key\":\"remark\",\"color\":null,\"title\":\"备注（选填）\"}]},\"redirectType\":1,\"materialType\":3,\"redirectUrl\":\"https://crm.ceshi112.com/proj/page/marketing/88146#/conference/detail?marketingActivityId=!!marketingActivityId!!&wxAppId=!!wxAppId!!&ea=88146&objectId=dfc79d4e8510413d90f309e24c7961c0\",\"materialId\":\"dfc79d4e8510413d90f309e24c7961c0\",\"sendRange\":4,\"filterNDaySentUser\":0,\"taPath\":null,\"marketingUserGroupIds\":[\"b1d17a181ff04f8d9247efc209f2082d\"],\"marketingUsers\":[{\"_parentIds\":[\"-2\",\"-3\"],\"id\":\"b1d17a181ff04f8d9247efc209f2082d\",\"name\":\" 短信短链测试 (4)\",\"type\":2,\"___visited___\":true,\"_parentId\":\"-2\",\"_domParentId\":\"-2\",\"_selected\":true,\"_time\":12,\"_semi\":false,\"id2\":\"\",\"more\":{\"id\":\"b1d17a181ff04f8d9247efc209f2082d\",\"name\":\" 短信短链测试 (4)\",\"ea\":\"88146\",\"description\":\"短信短链测试\",\"userNumber\":4,\"userPhoneNumber\":3,\"wxFansNumber\":1,\"emailUserNumber\":1,\"wxWorkExternalUserNumber\":1,\"ruleGroupJson\":[],\"createByName\":\"张珍菊\",\"createTime\":1692255419474,\"calculationTime\":1699945273000,\"status\":1,\"createBy\":1000,\"ruleGroupValue\":null,\"searchType\":2,\"tagOperator\":\"IN\",\"tagNames\":[],\"excludeTags\":false,\"excludeTagNames\":[],\"type\":2,\"calculationPeriod\":1,\"calculationStatus\":2,\"calculationFailReason\":\"success\",\"previousStartCalculateTime\":1699945272000,\"crmCustomerCount\":1,\"crmContactCount\":2,\"crmLeadCount\":80,\"canManualUpdate\":true,\"currentName\":\" 短信短链测试 (4)\"}}],\"type\":1,\"fixedTime\":\"\"},\"wechatMessageType\":1002,\"referer\":\"https://crm.ceshi112.com/XV/UI/Home#/app/marketing/index/=/meeting-marketing/dfc79d4e8510413d90f309e24c7961c0/meeting-detail\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}", AddMarketingActivityArg.class);
        weChatServiceManager.doAddAction(ea, fsUserId, addMarketingActivityArg);
        //全员推广图文消息
        NoticeSendArg vo = GsonUtil.fromJsonSerializingNull("{\"spreadType\":1,\"marketingEventId\":\"65659a68dc62af00011c13e7\",\"marketingActivityAuditData\":{\"executor\":\"郑辉、张珍菊、胡朋、林鸣、张嘉文、江淼玲、明桥、陈鸿曦、陈川、胡元焯、dove、罗勇、唐亚捷、廖媛珺、周浮洋、张树锋、l l le、杜晓阳、举杯送流年、Eswar 文\uD83D\uDC63、Han、罗雅茹、张玉佳\"},\"marketingActivityNoticeSendVO\":{\"title\":\"dddd\",\"content\":\"dfc79d4e8510413d90f309e24c7961c0\",\"contentType\":3,\"sendType\":1,\"startTime\":1701158403846,\"endTime\":1701417603846,\"description\":\"ddddd\",\"noticeVisibilityArg\":{\"departmentIds\":[],\"userIds\":[1011,1000,1006,1005,1017,1004,100000010,1009,1008,1002,100000007,1003,1007,1018,100000005,100000008,100000003,1001,100000014,100000009,100000013,1028,100000006],\"roles\":[],\"userGroups\":[]},\"addressBookType\":1,\"coverPath\":\"A_202311_28_fddf228cddec4f148d4784debe18a7a8.jpg\"},\"materialInfos\":[{\"objectId\":\"dfc79d4e8510413d90f309e24c7961c0\",\"contentType\":3}],\"referer\":\"https://crm.ceshi112.com/XV/UI/Home#/app/marketing/index/=/meeting-marketing/dfc79d4e8510413d90f309e24c7961c0/meeting-detail?type=radar&source=conference\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}", NoticeSendArg.class);
        noticeService.sendNotice(ea, fsUserId, vo);
        //...
    }

}
