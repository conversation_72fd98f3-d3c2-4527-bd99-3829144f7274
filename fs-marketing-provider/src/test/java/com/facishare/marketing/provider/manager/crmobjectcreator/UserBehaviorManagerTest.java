package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.facishare.marketing.provider.manager.UserRoleManager;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class UserBehaviorManagerTest extends BaseTest {

    @Autowired
    private UserBehaviorRecordObjManager userBehaviorRecordObjManager;

    @Autowired
    private UserRoleManager userRoleManager;

    @Test
    public void createObjDescribe() {
        String ea = "88162";
        userBehaviorRecordObjManager.getOrCreateObjDescribe(ea);
    }

    @Test
    public void addFunctionPrivilegeToUserRole() {
        String ea = "88146";
        userBehaviorRecordObjManager.addFunctionPrivilegeToUserRole(ea);
    }

    @Test
    public void addViewAllAndEditAllFuncCode() {
        String ea = "88146";
        userBehaviorRecordObjManager.addViewAllAndEditAllFuncCode(ea);
        userBehaviorRecordObjManager.addFunctionPrivilegeToUserRole(ea);
    }

    @Test
    public void addMemberField() {
        String ea = "76301";
        userBehaviorRecordObjManager.tryAddSpreadMemberId(ea);
    }

    @Test
    public void updateActionTypeOptions() {
        String ea = "88146";
        //String ea = "91234_sandbox";
        userBehaviorRecordObjManager.updateDescribeForCta(ea);
    }
}
