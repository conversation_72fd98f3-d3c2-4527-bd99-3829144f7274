package com.facishare.marketing.provider.manager.ai;

import com.facishare.ai.api.dto.PromptCompletions;
import com.facishare.marketing.api.vo.ai.PromptCompletionsArg;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class PaaSPromptManagerTest extends BaseTest {

    @Autowired
    private PaaSPromptManager paaSPromptManager;

    @Test
    public void testAgentInvoke() throws Exception {
        PromptCompletionsArg promptCompletionsArg = new PromptCompletionsArg();
        promptCompletionsArg.setObjectType(13);
        promptCompletionsArg.setObjectId("38c8989f6b2a466d8243613061ca881f");
        PromptCompletions.Result result = paaSPromptManager.generatePromotional("88146", 1000, promptCompletionsArg);
        System.out.println(result);
    }

}
