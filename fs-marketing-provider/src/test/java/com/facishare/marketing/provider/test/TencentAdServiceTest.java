package com.facishare.marketing.provider.test;

import com.facishare.marketing.api.result.baidu.GetDataOverviewResult;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.service.advertiser.TencentAdService;
import com.facishare.marketing.api.vo.advertiser.QueryTencentAdGroupListVO;
import com.facishare.marketing.api.vo.baidu.GetDataOverviewVO;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.advertiser.adAccount.TencentAdResult;
import com.facishare.marketing.provider.advertiser.tencent.TencentAdGroupResult;
import com.facishare.marketing.provider.advertiser.tencent.TencentCampaignResult;
import com.facishare.marketing.provider.advertiser.tencent.TencentKeywordDataResult;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.tencent.TencentAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.CampaignApiManager;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

@Slf4j
public class TencentAdServiceTest extends BaseTest {
    @Autowired
    private MarketingEventService marketingEventService;
    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private TencentAdMarketingManager tencentAdMarketingManager;
    @Autowired
    private RefreshDataManager refreshDataManager;
    @Autowired
    private AdAccountManager adAccountManager;
//    @Autowired
//    private MarketingAuditLogServiceImpl auditLogService;
    @Autowired
    private CampaignApiManager campaignApiManager;
    @Autowired
    private TencentAdService tencentAdService;
    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Test
    public void test() {
        marketingPromotionSourceObjManager.updateLeadsModuleAndDatFromOptions("88146");
    }

    @Test
    public void testTencentRefreshAllData() {
//        buildNewTraceContext();
//        marketingEventService.syncDataToTargetMarketingEventByRule();
//        marketingPromotionSourceObjManager.updateLeadsModuleAndDatFromOptions("88146");
        String ea = "83668";
        String adAccountId = "e0da465401a34e528a35c7907b1113ff";
        String source = AdSourceEnum.SOURCE_TENCETN.getSource();
        tencentAdMarketingManager.refreshAllData(ea, adAccountId, source);
        if(ea == "") {
        }
    }

    @Test
    public void testTencentRefreshTencentCampaign() {
        String ea = "88146";
        AdAccountEntity adAccountEntity = getAdAccountEntity();
        if (adAccountEntity == null) {
            return;
        }
        tencentAdMarketingManager.refreshTencentCampaign(ea, adAccountEntity);
        if(ea == "") {
        }
    }

    @Test
    public void testTencentRefreshTencentAdGroup() {
//        buildNewTraceContext();
//        marketingEventService.syncDataToTargetMarketingEventByRule();
        String ea = "88146";
        String adAccountId = "e0da465401a34e528a35c7907bcae3ee";
        String source = AdSourceEnum.SOURCE_TENCETN.getSource();
        AdAccountEntity adAccountEntity = adAccountManager.queryEnableAccountById(adAccountId);
        if (adAccountEntity == null) {
            return;
        }
        Date now = new Date();
        Date beginTime = DateUtil.minusDay(now, 7);
        tencentAdMarketingManager.refreshTencentAdGroup(ea, adAccountEntity, DateUtil.getTimesMorning(beginTime));
        if(ea == "") {
        }
    }

    @Test
    public void testTencentRefreshTencentKeyword() {
        String source = AdSourceEnum.SOURCE_TENCETN.getSource();
        AdAccountEntity adAccountEntity = getAdAccountEntity();
        if (adAccountEntity == null) {
            return;
        }
        tencentAdMarketingManager.refreshKeyword(adAccountEntity, source);
    }

    @Test
    public void testSyncAdKeywordToMarketingKeywordObj() {
        String ea = "88146";
        String adAccountId = "e0da465401a34e528a35c7907bcae3ee";
        String source = AdSourceEnum.SOURCE_TENCETN.getSource();
        refreshDataManager.syncAdKeywordToMarketingKeywordObj(ea, adAccountId, source);
    }

    @Test
    public void testSyncMarketingTermServingLinesDataByKeyword() {
        String ea = "83668";
        String adAccountId = "e0da465401a34e528a35c7907b1113ff";
        String source = AdSourceEnum.SOURCE_TENCETN.getSource();
        AdAccountEntity adAccountEntity = adAccountManager.queryEnableAccountById(adAccountId);
        if (adAccountEntity == null) {
            return;
        }
        String lastDayDate = DateUtil.parse(DateUtil.getSomeDay(new Date(), -1), DateUtil.DATE_FORMAT_DAY);
        refreshDataManager.syncMarketingTermServingLinesDataByKeyword(adAccountEntity, source, lastDayDate);
        if(ea == "") {
        }
    }

    private static void buildNewTraceContext() {
        try {
            TraceContext context = TraceContext.get();
            context.setTraceId(UUIDUtil.getUUID());
        } catch (Exception e) {
            log.error("initTraceContext error", e);
        }
    }

    private AdAccountEntity getAdAccountEntity() {
        String adAccountId = "e0da465401a34e528a35c7907b1113ff";
        AdAccountEntity adAccountEntity = adAccountManager.queryEnableAccountById(adAccountId);
        return adAccountEntity;
    }

    @Test
    public void testGetTencentAdGroup() {
        AdAccountEntity adAccountEntity = getAdAccountEntity();
        if (adAccountEntity == null) {
            return;
        }
        Date now = new Date();
        Date beginTime = DateUtil.minusDay(now, 7);
        List<String> apiVersions = Lists.newArrayList("v1.3", "v3.0");  //腾讯搜索广告使用v1.3接口获取；展示广告使用v3.0接口
        List<Long> adGroupIds = Lists.newArrayList();
        for (String v: apiVersions) {
            TencentAdResult<TencentAdGroupResult> result = campaignApiManager.getTencentAdGroup(adAccountEntity.getAccountId(), adAccountEntity.getToken(), 1, 100, DateUtil.getTimesMorning(beginTime), v);
            if(result != null && result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getList())) {
                result.getData().getList().forEach(d -> adGroupIds.add(d.getAdgroup_id()));
            }
        }

        for (String v: apiVersions) {
            TencentAdResult<TencentAdGroupResult> result = campaignApiManager.getTencentAdGroup(adAccountEntity.getAccountId(), adAccountEntity.getToken(), adGroupIds, "adgroup_id", v);
            if(result != null && result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getList())) {
            }
        }
    }

    @Test
    public void testGetTencentBidWordData() {
        long adAccountId = 25326632L;
        long adGroupId = 9394842042L;
        List<Long> keywordIdList = Lists.newArrayList(2702582479L);
        String token = "0425859627b92a5ca3d67bbdc9500399";
        TencentAdResult<TencentKeywordDataResult> result = campaignApiManager.getTencentBidWordDataV3(adAccountId, adGroupId, keywordIdList, token, 1, 100, "2024-12-09", "2024-12-09");
        log.info("v3 结果，result: {}", result);
        result = campaignApiManager.getTencentBidWordDataV1(adAccountId, adGroupId, keywordIdList, token, 1, 100, "2024-12-09", "2024-12-09");
        log.info("v1 结果，result: {}", result);
    }


    @Test
    public void testGetTencentAdGroupList() {
        QueryTencentAdGroupListVO vo = new QueryTencentAdGroupListVO();
        vo.setEa("88146");
//        vo.setAdGroupType("1");
        vo.setAdAccountId("e0da465401a34e528a35c7907bcae3ee");
        vo.setPageNum(1);
        vo.setPageSize(100);

        Result<PageResult<com.facishare.marketing.api.result.advertiser.tencent.TencentAdGroupResult>>
                result = tencentAdService.queryTencentAdGroupList(vo);

        if(result != null) {

        }
    }

    @Test
    public void testGetTencentDataOverview() {
        GetDataOverviewVO vo = new GetDataOverviewVO();
        vo.setEa("88146");
//        vo.setAdGroupType("1");
        vo.setAdAccountId("e0da465401a34e528a35c7907bcae3ee");
//        vo.setDataType("TENCENT_SHOW_AD_GROUP");
//        vo.setCampaignName("推广");

        Result<GetDataOverviewResult>
                result = tencentAdMarketingManager.getDataOverview(vo);

        if(result != null) {

        }
    }

    @Test
    public void getTencentCampaigns() {
        TencentAdResult<TencentCampaignResult> result = campaignApiManager.getTencentCampaigns(40916938L, "e9cff1f2ab100b2c1f7ca6047857ae11", 1, 10);
        log.info("result: {}", result);
    }
}
