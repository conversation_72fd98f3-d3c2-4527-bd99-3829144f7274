/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.marketing.api.arg.AddBoardCardArg;
import com.facishare.marketing.api.arg.AddBoardCardListArg;
import com.facishare.marketing.api.arg.AddBoardCardTaskArg;
import com.facishare.marketing.api.arg.AddEnterpriseTemplateArg;
import com.facishare.marketing.api.arg.ListBoardActivityArg;
import com.facishare.marketing.api.arg.ListBoardCardActivityArg;
import com.facishare.marketing.api.arg.UpdateBoardCardArg;
import com.facishare.marketing.api.arg.UpdateBoardCardTaskArg;
import com.facishare.marketing.api.arg.UpdateBoardMenuArg;
import com.facishare.marketing.api.arg.UpdateBoardNameArg;
import com.facishare.marketing.api.arg.UpdateBoardTemplateArg;
import com.facishare.marketing.api.arg.UpdateBoardUserArg;
import com.facishare.marketing.api.data.BoardTemplateData;
import com.facishare.marketing.api.data.BoardTemplateData.BoardCardListWithCardData;
import com.facishare.marketing.api.data.BoardTemplateData.BoardCardListWithCardData.BoardCardWithTask;
import com.facishare.marketing.api.data.BoardTemplateData.BoardCardListWithCardData.BoardCardWithTask.TaskData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.service.BoardService;
import com.facishare.marketing.api.vo.EnterpriseBoardTemplateVo;
import com.facishare.marketing.common.enums.BoardCardGoalType;
import com.facishare.marketing.common.enums.BoardCardStatus;
import com.facishare.marketing.common.enums.BoardCardTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.BoardCardActivityContentData;
import com.facishare.marketing.common.typehandlers.value.IntegerList;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.manager.BoardManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.github.autoconf.spring.reloadable.ReloadableProperty;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/*
 * <AUTHOR>
 * @Date 2020/8/6 11:14
 * @Version 1.0
 */
@Slf4j
public class BoardServiceTest extends BaseTest {
    @Autowired
    BoardService boardService;
    @Autowired
    BoardManager boardManager;
    private String boardTemplateList;

    @Test
    public void addSOPBoard(){
        BoardTemplateData arg = new BoardTemplateData();
        IntegerList boardUserIds = new IntegerList();
        boardUserIds.add(1000);
        arg.setBoardUserIds(boardUserIds);
        arg.setDefaultTemplateId("fb9824ef62f145b08a4efeb1f9066721");
        arg.setName("活动营销“新建市场活动10”的SOP看板");
        arg.setType("sop");
        arg.setObjectId("60acb15e52c2de0001738d43");
        Result<String> result = boardService.addBoard("74164", 1128, arg);
        System.out.println(result);
    }

    @Test
    public void addBoard(){
        BoardTemplateData arg = new BoardTemplateData();
        IntegerList boardUserIds = new IntegerList();
        boardUserIds.add(1128);
        arg.setBoardUserIds(boardUserIds);
        arg.setDefaultTemplateId("43dd8ee4321a4ffbbd9461869820dfb1");
        arg.setName("新的看板");
//        List<String> marketingUserGroupIds = new ArrayList<>();
//        marketingUserGroupIds.add("5194cb1421b1418391a49df796297cbc");
//        arg.setMarketingUserGroupIds(marketingUserGroupIds);
        arg.setVisibleRage("private");
        Result<String> result = boardService.addBoard("74164", 1128, arg);
        System.out.println(result);
    }

    @Test
    public void addBoardCardList(){
        AddBoardCardListArg addBoardCardListArg = new AddBoardCardListArg();
        addBoardCardListArg.setBoardId("33f3617e47554f9d9917c9dd7a9e6ee6");
        addBoardCardListArg.setName("列表junit");
        Result<String> result = boardService.addBoardCardList("74164", 1128, addBoardCardListArg);
        System.out.println(result);
    }

    @Test
    public void addBoardCard() {
        AddBoardCardArg addBoardCardArg = new AddBoardCardArg();
        addBoardCardArg.setBoardCardListId("01ecf6875ba646b794e084161a38dbd6");
        addBoardCardArg.setName("完成工作的卡片junit");
        addBoardCardArg.setType("common");
//        addBoardCardArg.setMarketingEventType(MarketingEventEnum.LIVE_MARKETING.getEventType());
//        addBoardCardArg.setMarketingEventId("5f1563fe7cc2c80001e6a025");
//        IntegerList principals = new IntegerList();
//        principals.add(1057);
//        addBoardCardArg.setPrincipals(principals);
        Result<String> result = boardService.addBoardCard("74164", 1128, addBoardCardArg);
        System.out.println(result);
    }

    @Test
    public void getBoardDetail(){
        Result<BoardResult> boardResultResult = boardService.getBoardDetail("88146", 1000, "2a64e10dc6754a4d93e1c3650111d7a0");
        System.out.println("====================");
        Map<String, BoardColumnData> boardColumnFormat = boardResultResult.getData().toBoardColumnFormat();
        String jsonString = JSON.toJSONString(boardColumnFormat);
        System.out.println(jsonString);
        System.out.println(boardResultResult.getData());
    }

    @Test
    public void addBoardCardTask() {
        AddBoardCardTaskArg arg = new AddBoardCardTaskArg();
        arg.setBoardCardId("57e707d1b7664b41a4a49a4c03b359ea");
        arg.setName("任务名称");
        arg.setExecutor(1036);
        boardService.addBoardCardTask("74164", 1040, arg);
    }

    @Test
    public void updateBoardCard() {
        UpdateBoardCardArg arg = new UpdateBoardCardArg();
        arg.setBoardId("33f3617e47554f9d9917c9dd7a9e6ee6");
        arg.setBoardCardId("fb6e68ac7be147f3b4a41bb82d80dfbd");
        arg.setName("完成工作的卡片junit");
//        IntegerList list = new IntegerList();
//        list.add(1036);
//        arg.setPrincipals(list);
        arg.setStatus(BoardCardStatus.FINISHED.getStatus());
        arg.setType(BoardCardTypeEnum.COMMON.getType());
        boardService.updateBoardCard("74164", 1128, arg);
    }


    @Test
    public void listBoardByFsUserId(){
        Result<List<BoardResult>> listResult = boardService.listBoardByFsUserId("78786", 1000);
        List<BoardResult> results = listResult.getData();
        results.forEach(System.out::println);
    }

    //删除
    @Test
    public void deleteBoardByBoardId(){
        Result<Boolean> booleanResult = boardService.deleteBoardByBoardId("74164", 1089, "83b31a45ef454e3cacb3ed14864a47d0");
        System.out.println(booleanResult);
    }

    @Test
    public void updateBoardByBoardId(){
        UpdateBoardNameArg arg = new UpdateBoardNameArg();
        arg.setId("a22d15536d7c417a92f1ec32921b3218");
        arg.setName("测试修改看板，同时description为null");
//        arg.setVisibleRange(BoardVisibleRangeEnum.PRIVATE_VISIBLE_RANGE.getVisibleRange());
//        boardService.updateBoardByBoardId("77741", 6, arg);
    }

    @Test
    public void addBoardUserByIds() {
        List<Integer> users = new LinkedList<>();
        users.add(666);
        users.add(999);
        users.add(888);
//        boardService.addBoardUserByIds("77741", 6, "a22d15536d7c417a92f1ec32921b3218", users);
    }

    @Test
    public void deleteBoardUserByIds() {
        List<Integer> users = new LinkedList<>();
        users.add(666);
        users.add(999);
        users.add(888);
//        boardService.deleteBoardUserByIds("77741", 6, "a22d15536d7c417a92f1ec32921b3218", users);
    }

    @Test
    public void updateBoardUserByBoardId() {
        UpdateBoardUserArg arg = new UpdateBoardUserArg();
        arg.setBoardId("fff");
        List<Integer> addUsers = new LinkedList<>();
        addUsers.add(10);
        addUsers.add(11);
        addUsers.add(130);
        addUsers.add(131);
        arg.setBoardUserIds(addUsers);
        boardService.updateBoardUserByBoardId("77741", 12, arg);

        UpdateBoardUserArg arg1 = new UpdateBoardUserArg();
        arg1.setBoardId("fff");
        List<Integer> updateUsers = new LinkedList<>();
        updateUsers.add(12);
        updateUsers.add(13);
        updateUsers.add(131);
        updateUsers.add(132);
        arg1.setBoardUserIds(updateUsers);
        boardService.updateBoardUserByBoardId("77741", 11, arg1);
    }

    @Test
    public void addBoardCardActivity() {
        BoardCardActivityContentData contentData = new BoardCardActivityContentData();
//        contentData.setComment("到此一游");
//        boardManager.addBoardCardActivity("74164", 1040, "aaaa", "aaaaaaa", BoardCardActivityActionTypeEnum.COMMENT.getActionType(), contentData);
//        contentData.setBoardCardName("最牛的卡片名");
//        boardManager.addBoardCardActivity("74164", 1040, "aaaa", "aaaaaaa", BoardCardActivityActionTypeEnum.CREATE_CARD.getActionType(), contentData);
//        contentData.setBoardCardTaskName("任务1");
//        contentData.setUpdateBoardCardTaskStartTime(new Date().getTime());
//        boardManager.addBoardCardActivity("74164", 1040, "aaaa", "aaaaaaa", BoardCardActivityActionTypeEnum.UPDATE_TASK_START_TIME.getActionType(), contentData);
//        contentData.setBoardCardDescription("最牛的描述信息");
//        boardManager.addBoardCardActivity("74164", 1040, "aaaa", "aaaaaaa", BoardCardActivityActionTypeEnum.UPDATE_CARD_DESCRIPTION.getActionType(), contentData);
//        contentData.setBoardCardPrincipal(1040);
//        boardManager.addBoardCardActivity("74164", 1040, "aaaa", "aaaaaaa", BoardCardActivityActionTypeEnum.UPDATE_CARD_PRINCIPAL.getActionType(), contentData);
//        contentData.setAssociateMarketingEventName("快闪活动");
//        contentData.setBoardCardPrincipal(1040);
//        boardManager.addBoardCardActivity("74164", 1040, "aaaa", "aaaaaaa", BoardCardActivityActionTypeEnum.ASSOCIATE_CARD_MARKETING_EVENT.getActionType(), contentData);
//        contentData.setMoveBoardCardFromBoardCardListName("牛逼的卡片名称");
//        contentData.setMoveBoardCardToBoardCardListName("更加牛逼的卡片名称");
//        boardManager.addBoardCardActivity("74164", 1040, "aaaa", "aaaaaaa", BoardCardActivityActionTypeEnum.MOVE_BOARD_CARD.getActionType(), contentData);

    }

    @Test
    public void listBoardCardActivityByBoardCardId(){
        ListBoardCardActivityArg arg = new ListBoardCardActivityArg();
        arg.setBoardCardId("2f70ed97dffa41a4927ab2b694cc43ee");

        List<BoardCardActivityResult> results = boardManager.listBoardCardActivityByBoardCardId("74164", arg);

        arg.setPageNo(1);
        arg.setPageSize(200);
        List<BoardCardActivityResult> results1 = boardManager.listBoardCardActivityByBoardCardId("74164", arg);

        arg.setPageNo(1);
        arg.setPageSize(2);
        List<BoardCardActivityResult> results2 = boardManager.listBoardCardActivityByBoardCardId("74164", arg);

        BoardCardActivityResult latestResult = boardManager.getLatestBoardCardActivityByBoardCardId("74164", "57e707d1b7664b41a4a49a4c03b359ea");

        System.out.println(results1);
        System.out.println(results2);
        System.out.println(latestResult);
    }

    @Test
    public void addBoardList() {
        AddBoardCardListArg arg = new AddBoardCardListArg();
        arg.setBoardId("bbbbbbbbbbb");
        arg.setName("列表之666");
        boardService.addBoardCardList("74164", 1040, arg);
    }

    @Test
    public void updateBoardCardTask() {
        UpdateBoardCardTaskArg arg = new UpdateBoardCardTaskArg();
        arg.setBoardId("bbbbbbbbbbb");
        arg.setBoardCardId("57e707d1b7664b41a4a49a4c03b359ea");
        arg.setId("7f0c8bd69ee14d94ab5892f40c9d5c98");
        arg.setName("更新任务");
        arg.setExecutor(1035);
        boardService.updateBoardCardTask("74164", 1040, arg);
    }

    @Test
    public void boardCardActivityTest() {
        String boardId = "bbbbbbbbbbbbb";
        String ea = "74164";
        Integer operator = 1057;
        String marketingEventId = "5f324cd51c1d070001fe0fa3";

        AddBoardCardListArg arg = new AddBoardCardListArg();
        arg.setBoardId(boardId);
        arg.setName("我的列表");
        String boardCardListId = boardService.addBoardCardList(ea, operator, arg).getData();

        AddBoardCardArg addBoardCardArg = new AddBoardCardArg();
        addBoardCardArg.setBoardCardListId(boardCardListId);
        addBoardCardArg.setName("我的卡片");
        addBoardCardArg.setType(BoardCardTypeEnum.MARKETING_EVENT_DATA.getType());
        addBoardCardArg.setMarketingEventId(marketingEventId);
        addBoardCardArg.setGoalType(BoardCardGoalType.PV.getType());
        addBoardCardArg.setGoalValue(99999);
        String boardCardId = boardService.addBoardCard(ea, operator, addBoardCardArg).getData();

        UpdateBoardCardArg updateBoardCardArg = new UpdateBoardCardArg();
        updateBoardCardArg.setBoardId(boardId);
        updateBoardCardArg.setBoardCardId(boardCardId);
        updateBoardCardArg.setName("我的卡片2");
        IntegerList list = new IntegerList();
        //添加负责人！！！
        list.add(1031);
        updateBoardCardArg.setPrincipals(list);
        updateBoardCardArg.setStatus(BoardCardStatus.NOT_START.getStatus());
        updateBoardCardArg.setType(BoardCardTypeEnum.MARKETING_EVENT_DATA.getType());
        boardService.updateBoardCard(ea, operator, updateBoardCardArg);

        AddBoardCardTaskArg addBoardCardTaskArg = new AddBoardCardTaskArg();
        addBoardCardTaskArg.setBoardCardId(boardCardId);
        addBoardCardTaskArg.setName("任务名称");
        //指派任务！！！
        addBoardCardTaskArg.setExecutor(1033);
        String boardCardTaskId = boardService.addBoardCardTask(ea, operator, addBoardCardTaskArg).getData();

        UpdateBoardCardTaskArg updateBoardCardTaskArg = new UpdateBoardCardTaskArg();
        updateBoardCardTaskArg.setBoardId(boardId);
        updateBoardCardTaskArg.setBoardCardId(boardCardId);
        updateBoardCardTaskArg.setId(boardCardTaskId);
        updateBoardCardTaskArg.setName("更新任务");
        //认领任务！！！
        updateBoardCardTaskArg.setExecutor(operator);
        boardService.updateBoardCardTask(ea, operator, updateBoardCardTaskArg);

//        List<BoardCardActivityResult> results = boardManager.listBoardCardActivityByBoardCardId(ea, boardCardId);
//        System.out.println(results);
    }

    @Test
    public void addComment() {
        String boardId = "bbbbbbbbbbbbb";
        String ea = "74164";
        Integer operator = 1057;
        String boardCardId = "70b9fb995dac4fdfaa91227baa0cfc2c";
        boardService.addCardComment(ea, operator, boardId, boardCardId, "添加了一条留言");
//        List<BoardCardActivityResult> results = boardManager.listBoardCardActivityByBoardCardId(ea, boardCardId);
//        System.out.println(results);
    }

    @Test
    public void getLatestBoardCardActivityByBoardCardId() {
        boardManager.getLatestBoardCardActivityByBoardCardId("74164", "2f70ed97dffa41a4927ab2b694cc43ee");
    }

    @Test
    public void getBoardCardDetailWithBoardNameAndBoardCardListName() {
        BoardCardResult result = boardService.getBoardCardDetailWithBoardNameAndBoardCardListName("74164", 1128, "73e123cc9d3c424cbaba640d0020c13b").getData();
        System.out.println("======================================");
        System.out.println(result);
    }

    @Test
    public void listEnterpriseBoardTemplate() {
        Result<List<EnterpriseBoardTemplateVo>> listResult = boardService.listEnterpriseBoardTemplate("74164", 1128);
        System.out.println(listResult);
    }

    @Test
    public void addEnterprise() {
        AddEnterpriseTemplateArg arg = new AddEnterpriseTemplateArg();
        arg.setName("cde");
        arg.setSceneType("live");
        arg.setCover("A_202105_25_1aac6a52af444358a4c8b1e47c40e3a4.jpg");
        Result<EnterpriseBoardTemplateVo> enterpriseBoardTemplateVoResult = boardService.addEnterpriseTemplate("74164", 1128, arg);
        System.out.println(enterpriseBoardTemplateVoResult);
    }

    @Test
    public void updateBoardMenu() {
        UpdateBoardMenuArg arg = new UpdateBoardMenuArg();
        arg.setBoardId("33f3617e47554f9d9917c9dd7a9e6ee6");
//        arg.setGoalType("group_user_count");
        arg.setGoalType("work_finished_count");
//        arg.setGoalValue(12);
//        arg.setGoalUserGroupId("93b11c78cf974c6f848d3f2791b37760");
//        arg.setGoalValue(132);
//        arg.setMarketingEventId("5f0848e99cc0c400015aca46");
//        arg.setMarketingEventType("content_marketing");
        arg.setAssociatedObjectType("work_finished_count");
        System.out.println("====================");
        System.out.println(boardService.updateBoardMenu("74164", 1128, arg));
    }

    @Test
    public void getBoardMenuDetail() {
//        BoardMenuResult result = boardService.getBoardMenuDetail("74164", 1128, "33f3617e47554f9d9917c9dd7a9e6ee6").getData();
        BoardMenuResult result = boardService.getBoardMenuDetail("74164", 1365, "2fc1188587d8402b9eb69318fbacf480").getData();
        System.out.println("=================");
        System.out.println(result);
    }

    @Test
    public void listBoardActivityByBoardId() {
        ListBoardActivityArg arg = new ListBoardActivityArg();
        arg.setBoardId("33f3617e47554f9d9917c9dd7a9e6ee6");
        List<BoardCardActivityResult> list = boardService.listBoardActivityByBoardId("74164", arg).getData();
        System.out.println("=============================");
        for (BoardCardActivityResult activityResult : list) {
            System.out.println(activityResult);
        }
    }

    @Test
    public void listBoardTemplate() {
        List<EnterpriseBoardTemplateVo> boardTemplateDataList = boardService.listBoardTemplate("74164", 1128, false).getData();
        System.out.println("=============");
        for (EnterpriseBoardTemplateVo boardTemplateData : boardTemplateDataList) {
            System.out.println(boardTemplateData.getName());
        }
    }

    @Test
    public void updateBoardNameByBoardId() {
        UpdateBoardNameArg arg = new UpdateBoardNameArg();
        arg.setId("dca566b9afed4cccb887a133e7687d5f");
        arg.setName("888");
        Result<Boolean> result = boardService.updateBoardNameByBoardId("74164", 1128, arg);
        System.out.println("===========");
        System.out.println(result);
    }

    @Test
    public void updateTemplateInfo() {
        UpdateBoardTemplateArg arg = new UpdateBoardTemplateArg();
        arg.setId("fb9824ef62f145b08a4efeb1f9066721");
        arg.setName("修改成功");
        arg.setSceneType("marketing_event");
        arg.setCover("https://www.ceshi112.com/FSC/N/FileShare/ShowImage?fileId=E856D8C119D34275661A64CA2E01E415D6119511A301BB2A9217EA5D6413429BBFEE0E59476514BC675ABEE699F3810369747DBA3F7E3EF20AFA8D925EF3BDB6638A09E668694E13");
        System.out.println(boardService.updateTemplateInfo("74164", 1128, arg).getData());
    }

    @Test
    public void listBoardTemplateBySceneType() {
        List<EnterpriseBoardTemplateVo> conference = boardService.listBoardTemplateBySceneType("74164", 1128, "conference").getData();
        System.out.println("================");
        conference.forEach(System.out::println);
    }

    @Test
    public void getSOPTabInfo() {
        SOPBoardTabResult conference = boardService.getSOPTabInfo("74164", 1128, null).getData();
        System.out.println("===========");
        System.out.println(conference);
    }

    @Test
    public void sk() {
//        GsonUtil.fromJson(boardTemplateList, new TypeToken<List<BoardTemplateData>>(){}.getType());
        HashMap<String, String> nameToBoardId = new HashMap<>();
        nameToBoardId.put("月度运营看板", "YDYYKB");
        nameToBoardId.put("市场运营", "SCYY");
        nameToBoardId.put("内容营销", "NRYX");
        nameToBoardId.put("日常直播运营", "RCZBYY");
        nameToBoardId.put("大型直播活动", "DXZBHD");
        nameToBoardId.put("活动规划", "HDGH");
        nameToBoardId.put("ABM营销获客", "ABMYXHK");
        nameToBoardId.put("飞轮集客营销", "FLJKYX");
        nameToBoardId.put("社群营销", "SQYX");
        nameToBoardId.put("SEO自检清单", "SEOZJQD");
        nameToBoardId.put("合伙人裂变增长计划", "HHRLBZZJH");
        nameToBoardId.put("公众号运营", "GZHYY");
        nameToBoardId.put("渠道转化分析", "QDZHFX");
        nameToBoardId.put("空白模板", "KBMB");
        ArrayList<String> listSQLList = new ArrayList<>();
        ArrayList<String> cardSQLList = new ArrayList<>();
        ArrayList<String> taskSQLList = new ArrayList<>();
        List<BoardTemplateData> boardTemplateData = JSON.parseArray(boardTemplateList, BoardTemplateData.class);
        for (BoardTemplateData data : boardTemplateData) {
            String boardId = nameToBoardId.get(data.getName());
            List<BoardCardListWithCardData> boardCardLists = data.getBoardCardLists();
            int time1 = 52355;
            for (BoardCardListWithCardData boardCardList : boardCardLists) {
                String boardCardListId = UUIDUtil.getUUID();
                String listSQL = "INSERT INTO \"public\".\"board_card_list\" VALUES ('" + boardCardListId + "', '__SYSTEM', '";
                listSQL += boardId;
                listSQL += "', '";
                listSQL += boardCardList.getName();
                listSQL += "', -10000, '2021-04-22 17:03:42." + --time1 + "', '2021-04-22 17:03:42." + time1 + "', 0);";
                listSQLList.add(listSQL);

                List<BoardCardWithTask> boardCards = boardCardList.getBoardCards();
                int time2 = 52255;
                for (BoardCardWithTask boardCard : boardCards) {
                    String boardCardId = UUIDUtil.getUUID();
                    String cardSQL = "INSERT INTO \"public\".\"board_card\" VALUES ('" + boardCardId + "', '__SYSTEM', '" + boardId + "', '" + boardCardListId + "', '";
                    cardSQL += boardCard.getName();
                    cardSQL += "', ";
                    if (boardCard.getDescription() != null) {
                        cardSQL += "'" + boardCard.getDescription() + "'";
                    } else {
                        cardSQL += "NULL";
                    }
                    cardSQL += ", NULL, 'not_start', NULL, NULL, '";
                    cardSQL += boardCard.getType();
                    cardSQL += "', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'f', '2021-04-22 17:03:42." + --time2 + "', '2021-04-22 17:03:42." + time2 + "', -10000, 0);";
                    cardSQLList.add(cardSQL);

                    List<TaskData> tasks = boardCard.getTasks();
                    int time3 = 52155;
                    for (TaskData task : tasks) {
                        String taskId = UUIDUtil.getUUID();
                        String taskSQL = "INSERT INTO \"public\".\"board_card_task\" VALUES ('" + taskId + "', '__SYSTEM', '" + boardId + "', '" + boardCardId + "', '";
                        taskSQL += task.getName();
                        taskSQL += "', NULL, NULL, NULL, NULL, 'f', 'f', 'not_start', -10000, '2021-04-22 17:03:42." + --time3 + "', '2021-04-22 17:03:42." + time3 + "', 0);";
                        taskSQLList.add(taskSQL);
                    }
                }
            }
        }
        System.out.println("==================================================");
        System.out.println("board_card_list");
        listSQLList.forEach(System.out::println);
        System.out.println("==================================================");
        System.out.println("board_card");
        cardSQLList.forEach(System.out::println);
        System.out.println("==================================================");
        System.out.println("board_card_task");
        taskSQLList.forEach(System.out::println);
    }

    public static void main(String[] args) {
        String str = "{\"errCode\":0,\"errMsg\":\"成功\",\"data\":{\"id\":\"bb899e97e7974557a8333428fed4dfe0\",\"name\":\"线下会议\",\"type\":\"data\",\"description\":null,\"cover\":\"https://km.fxiaoke.com/fs-kemai-web/static/marketing-web/空白模板.jpg\",\"creatorId\":null,\"createTime\":1622447473478,\"boardCardLists\":[{\"id\":\"e410b90fed3c49729243f272bb72814e\",\"name\":\"会议策划\",\"creator\":1005,\"boardCards\":[{\"id\":\"e80dafef6067420682d8b1783c1a83d7\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"e410b90fed3c49729243f272bb72814e\",\"name\":\"会议主题与定位\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":3,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_task\",\"contentTitle\":\"肖海丽 创建了任务：确定会议主题\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622514871078,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"68b38a77db2a487e945535af917d6484\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"e410b90fed3c49729243f272bb72814e\",\"name\":\"会议目标\",\"boardName\":null,\"boardCardListName\":null,\"description\":\"报名人数、签到率\\n\",\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_description\",\"contentTitle\":\"肖海丽 更新了备注:\",\"contentDetail\":\"报名人数、签到率\\n\",\"operator\":\"1005\",\"createTime\":1622520023217,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"14629046ea8d48e3a7755db759f67f63\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"e410b90fed3c49729243f272bb72814e\",\"name\":\"会议时间地点\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":1,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_task\",\"contentTitle\":\"肖海丽 创建了任务：会场选址\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622460771318,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"1ad0044163754d8c92366e1fc727abdc\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"e410b90fed3c49729243f272bb72814e\",\"name\":\"预算费用\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":3,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_task\",\"contentTitle\":\"肖海丽 创建了任务：嘉宾费用\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622454743027,\"commentAvatar\":null,\"createBySystem\":false}}]},{\"id\":\"9661b21e955f461d8dad29ed300d3750\",\"name\":\"会议筹备\",\"creator\":1005,\"boardCards\":[{\"id\":\"e4a1a916998a404d8e60ba6a8f5d8bd2\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"9661b21e955f461d8dad29ed300d3750\",\"name\":\"会议流程确定\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:会议流程确定\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622518857810,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"b751bc2602c3436fad98944e3f09c9eb\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"9661b21e955f461d8dad29ed300d3750\",\"name\":\"会务组成员确定\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:会务组成员确定\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622518866129,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"42fd7bad37d84bc581022794d258be21\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"9661b21e955f461d8dad29ed300d3750\",\"name\":\"会场预定\",\"boardName\":null,\"boardCardListName\":null,\"description\":\"会议室、住宿酒店预定\\n\",\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_description\",\"contentTitle\":\"肖海丽 更新了备注:\",\"contentDetail\":\"会议室、住宿酒店预定\\n\",\"operator\":\"1005\",\"createTime\":1622519747663,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"16929525cb9947fc99fc9f33c55f31e9\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"9661b21e955f461d8dad29ed300d3750\",\"name\":\"物料管理\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":2,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_task\",\"contentTitle\":\"肖海丽 创建了任务：参会指南\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622519687934,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"0e1b2fbd73244febb650a11f1ddbf9ca\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"9661b21e955f461d8dad29ed300d3750\",\"name\":\"报名设置\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":3,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_task_name\",\"contentTitle\":\"肖海丽 修改了任务：'参会人数确认'为'确认报名人数'\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622515249822,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"69ab2a0845a54b1faa0133af359169cf\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"9661b21e955f461d8dad29ed300d3750\",\"name\":\"自动会议通知设置\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":6,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:自动会议通知设置\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622519634142,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"07cf8ccb09284565a2344aa2af072c77\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"9661b21e955f461d8dad29ed300d3750\",\"name\":\"特邀嘉宾邀请\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:特邀嘉宾邀请\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622515188600,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"e3857de927ce4c77b6d556869354e9cd\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"9661b21e955f461d8dad29ed300d3750\",\"name\":\"赞助方案、合作商邀请\",\"boardName\":null,\"boardCardListName\":null,\"description\":\"赞助方案：\\n\\n1. 冠名赞助\\n2. 协力赞助\\n3. 友情赞助\\n4. 指定赞助\\n\",\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_description\",\"contentTitle\":\"肖海丽 更新了备注:\",\"contentDetail\":\"赞助方案：\\n\\n1. 冠名赞助\\n2. 协力赞助\\n3. 友情赞助\\n4. 指定赞助\\n\",\"operator\":\"1005\",\"createTime\":1622460114642,\"commentAvatar\":null,\"createBySystem\":false}}]},{\"id\":\"8d9ff3fa068c45d491c219fb5a93a0d4\",\"name\":\"邀约推广\",\"creator\":1005,\"boardCards\":[{\"id\":\"8003ac8bc3344330a184943a43808084\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"8d9ff3fa068c45d491c219fb5a93a0d4\",\"name\":\"员工邀约\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":1,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_task\",\"contentTitle\":\"肖海丽 创建了任务：发送全员推广或邀约任务\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1623068469442,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"c9fba554166e4c1f9e976fc28a57affe\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"8d9ff3fa068c45d491c219fb5a93a0d4\",\"name\":\"邮件邀约\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_card\",\"contentTitle\":\"肖海丽 创建了工作项:邮件邀约\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622448147236,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"5f2cb65f068548419bff0959930fbf1e\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"8d9ff3fa068c45d491c219fb5a93a0d4\",\"name\":\"公众号宣传\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:公众号宣传\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622459837788,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"bedf63504fc94832a70fd4618e5d3533\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"8d9ff3fa068c45d491c219fb5a93a0d4\",\"name\":\"海报宣传\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_card\",\"contentTitle\":\"肖海丽 创建了工作项:海报宣传\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622459825181,\"commentAvatar\":null,\"createBySystem\":false}}]},{\"id\":\"a4200265eafb4d44bd6110f5ccd33285\",\"name\":\"报名管理\",\"creator\":1005,\"boardCards\":[{\"id\":\"2c9913b76b6d400ebfeca89c23bdc928\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"a4200265eafb4d44bd6110f5ccd33285\",\"name\":\"参会人员管理\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":2,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"move_board_card\",\"contentTitle\":\"肖海丽 将工作项从'会议现场'移动到'报名管理'\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622518807690,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"52b3e3e02f7948fca9ba9878ee171ade\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"a4200265eafb4d44bd6110f5ccd33285\",\"name\":\"活动通知\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":1,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_task\",\"contentTitle\":\"肖海丽 创建了任务：报名成功通知或报名审核结果通知\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622455201322,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"6259159df9c34e46a093e97088e6fca2\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"a4200265eafb4d44bd6110f5ccd33285\",\"name\":\"会前通知确认\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":1,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"move_board_card\",\"contentTitle\":\"肖海丽 将工作项从'会议现场'移动到'报名管理'\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622517018565,\"commentAvatar\":null,\"createBySystem\":false}}]},{\"id\":\"b9b941da4d504af3810ffa58cd014f4f\",\"name\":\"会议现场\",\"creator\":1005,\"boardCards\":[{\"id\":\"c0c9439a360a43009f19bd542e25e0db\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"b9b941da4d504af3810ffa58cd014f4f\",\"name\":\"会前准备\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":2,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:会前准备\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622516917641,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"7a50bfbf8c1d4b24900f64972ecf646e\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"b9b941da4d504af3810ffa58cd014f4f\",\"name\":\"会议签到入场\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":2,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:会议签到入场\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622517293449,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"a4b2e9201c964f47a88979f75a4b5e5f\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"b9b941da4d504af3810ffa58cd014f4f\",\"name\":\"现场流程管理\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":6,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_task\",\"contentTitle\":\"肖海丽 创建了任务：应急预案\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1623068565418,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"410a517da6f3435f9769b9e7aa3745a1\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"b9b941da4d504af3810ffa58cd014f4f\",\"name\":\"现场互动\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":1,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"delete_task\",\"contentTitle\":\"肖海丽 删除了任务：问卷调查\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1623068607868,\"commentAvatar\":null,\"createBySystem\":false}}]},{\"id\":\"4cd39a65749f4d03a68a9882f226c23a\",\"name\":\"会后总结\",\"creator\":1005,\"boardCards\":[{\"id\":\"90c9661671b04e429b8899e7b1746408\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"4cd39a65749f4d03a68a9882f226c23a\",\"name\":\"会后感谢通知\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"create_card\",\"contentTitle\":\"肖海丽 创建了工作项:会后感谢通知\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622455290242,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"3fc9b9c038de4d248b9dae43bbab9fda\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"4cd39a65749f4d03a68a9882f226c23a\",\"name\":\"参会客户跟踪回访\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:参会客户跟踪回访\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1623068640647,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"4d31187d58cf4c7fb405694a928c4f3e\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"4cd39a65749f4d03a68a9882f226c23a\",\"name\":\"会议精彩回顾推文\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:会议精彩回顾推文\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622455331930,\"commentAvatar\":null,\"createBySystem\":false}},{\"id\":\"2029fc762b88442a8cd9c1023ce9a74b\",\"boardId\":\"bb899e97e7974557a8333428fed4dfe0\",\"boardCardListId\":\"4cd39a65749f4d03a68a9882f226c23a\",\"name\":\"会议效果评估\",\"boardName\":null,\"boardCardListName\":null,\"description\":null,\"principals\":null,\"principalsName\":null,\"creator\":1005,\"status\":\"not_start\",\"startTime\":null,\"endTime\":null,\"type\":\"common\",\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"targetObjectName\":null,\"goalType\":null,\"goalValue\":null,\"taskList\":null,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":null},\"boardCardTaskListVersion\":null,\"latestBoardCardActivity\":{\"actionType\":\"update_card_name\",\"contentTitle\":\"肖海丽 更新了标题:会议效果评估\",\"contentDetail\":null,\"operator\":\"1005\",\"createTime\":1622460421216,\"commentAvatar\":null,\"createBySystem\":false}}]}],\"boardCardListVersion\":14,\"boardCardNum\":null,\"boardCardTaskNum\":null,\"visibleRange\":\"public\",\"boardUsers\":null,\"templateType\":0,\"haveAuthority\":null,\"targetObjectName\":null,\"goalType\":\"work_finished_count\",\"goalValue\":27,\"statisticData\":{\"pv\":null,\"uv\":null,\"leadUserCount\":null,\"openEmailUserCount\":null,\"spreadEmployeeCount\":null,\"allNeedSpreadEmployeeCount\":null,\"totalSendUserCount\":null,\"enrollUserCount\":null,\"checkInUserCount\":null,\"liveViewCount\":null,\"distributorCount\":null,\"externalUserCount\":null,\"finishedTaskCount\":null,\"totalTaskCount\":null,\"groupUserCount\":null,\"workFinishedCount\":0},\"marketingUserGroupIds\":[],\"marketingActivityType\":null,\"marketingActivityId\":null,\"marketingEventType\":null,\"marketingEventId\":null,\"distributePlanId\":null,\"goalUserGroupId\":null,\"associatedObjectType\":\"work_finished_count\"}}";
        Result<BoardResult> resultObject = JSON.parseObject(str, new TypeReference<Result<BoardResult>>() {
        });
        BoardResult data = resultObject.getData();
        List<BoardCardListResult> boardCardLists = data.getBoardCardLists();
        ArrayList<String> listSQLList = new ArrayList<>();
        ArrayList<String> cardSQLList = new ArrayList<>();
        String boardId = "XXHY";
        int time = 52355;
        for (BoardCardListResult boardCardList : boardCardLists) {
            String boardCardListId = UUIDUtil.getUUID();
            int time1 = 52355;
            String listSQL = "INSERT INTO \"public\".\"board_card_list\" VALUES ('" + boardCardListId + "', '__SYSTEM', '";
            listSQL += boardId;
            listSQL += "', '";
            listSQL += boardCardList.getName();
            listSQL += "', -10000, '2021-04-22 17:03:42." + --time + "', '2021-04-22 17:03:42." + time + "', 0);";
            listSQLList.add(listSQL);

            List<BoardCardResult> boardCards = boardCardList.getBoardCards();
            for (BoardCardResult boardCard : boardCards) {
                String boardCardId = UUIDUtil.getUUID();
                String cardSQL = "INSERT INTO \"public\".\"board_card\" VALUES ('" + boardCardId + "', '__SYSTEM', '" + boardId + "', '" + boardCardListId + "', '";
                cardSQL += boardCard.getName();
                cardSQL += "', ";
                if (boardCard.getDescription() != null) {
                    cardSQL += "'" + boardCard.getDescription() + "'";
                } else {
                    cardSQL += "NULL";
                }
                cardSQL += ", NULL, 'not_start', NULL, NULL, '";
                cardSQL += boardCard.getType();
                cardSQL += "', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'f', '2021-04-22 17:03:42." + --time1 + "', '2021-04-22 17:03:42." + time1 + "', -10000, 0);";
                cardSQLList.add(cardSQL);
            }
        }
        System.out.println("==================================================");
        System.out.println("board_card_list");
        listSQLList.forEach(System.out::println);
        System.out.println("==================================================");
        System.out.println("board_card");
        cardSQLList.forEach(System.out::println);
    }

}

