package com.facishare.marketing.provider.service.advertiser.headlines;

import com.facishare.marketing.api.arg.advertiser.BaiduAuthCallBackArg;
import com.facishare.marketing.api.service.advertiser.headlines.AuthCallbackService;
import com.facishare.marketing.api.vo.advertiser.headlines.GetAccessTokenVo;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.manager.baidu.CampaignApiManager;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class AuthCallbackServiceImplTest extends BaseTest {

    @Autowired
    private AuthCallbackService authCallbackService;

    @Autowired
    private CampaignApiManager campaignApiManager;

    @Test
    public void getHeadlinesAuthInfo() {
        GetAccessTokenVo getAccessTokenVo = new GetAccessTokenVo();
        getAccessTokenVo.setEa("88146");
        getAccessTokenVo.setSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        getAccessTokenVo.setAuthCode("9c91e9866f4b4a9bcff743de4835880566d93b24");
    }

    @Test
    public void getLocalAccountInfo() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setAccountId(1813788678825163L);
        campaignApiManager.getLocalAccountInfo(adAccountEntity, "1b2a0a25a77fd651a26810210eb1bd7facc4e166");
    }

    @Test
    public void baiduAuthCallBack() {
        BaiduAuthCallBackArg authCallBackArg = new BaiduAuthCallBackArg();
        authCallBackArg.setState("88146");
        authCallbackService.baiduAuthCallBack(authCallBackArg);
    }

}
