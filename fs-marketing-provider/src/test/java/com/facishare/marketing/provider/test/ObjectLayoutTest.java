/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test;

import com.facishare.marketing.api.arg.usermarketingaccount.GetPageUserMarketingAccountByObjectArg;
import com.facishare.marketing.api.result.EmployeeMsgResult;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingAccountDetailsResult;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.service.marketingplugin.MarketingUserPluginService;
import com.facishare.marketing.provider.manager.MarketingPluginConfigManager;
import com.fxiaoke.crmrestapi.arg.FindLayoutArg;
import com.fxiaoke.crmrestapi.arg.UpdateLayoutArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.LayoutDescribe;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.result.FindLayoutResult;
import com.fxiaoke.crmrestapi.service.ObjectLayoutService;
import com.google.gson.internal.LinkedTreeMap;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext-zfy-test.xml")
public class ObjectLayoutTest {
    @BeforeClass
    public static void setUp() {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name", "fs-marketing-provider");
    }

    @Autowired
    private ObjectLayoutService objectLayoutService;

    @Autowired
    private UserMarketingAccountService userMarketingAccountService;

    @Autowired
    private MarketingPluginConfigManager marketingPluginConfigManager;

    @Autowired
    private MarketingUserPluginService marketingUserPluginService;

    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;

    @Test
    public void updateLayout(){
        FindLayoutArg arg = new FindLayoutArg();
        arg.setObjectDescribeApiName("EnterpriseInfoObj");
        arg.setApiName("EnterpriseInfoObj_layout_generate_by_UDObjectServer__c");
        InnerResult<FindLayoutResult> layout = objectLayoutService.findLayout(HeaderObj.newInstance(88146, -10000), arg);
        FindLayoutResult result = layout.getResult();
        LayoutDescribe getLayout = result.getLayout();
        List<LinkedTreeMap> componentsList = (List<LinkedTreeMap>) getLayout.get("components");
        Iterator<LinkedTreeMap> iterator = componentsList.iterator();
        while (iterator.hasNext()) {
            LinkedTreeMap it = iterator.next();
            if (Objects.equals(it.get("type"),"tabs")) {
                List<LinkedTreeMap> tabsMap = (List<LinkedTreeMap>) it.get("tabs");
                Iterator<LinkedTreeMap> treeMapIterator = tabsMap.iterator();
                while (treeMapIterator.hasNext()) {
                    LinkedTreeMap next = treeMapIterator.next();
                    if (Objects.equals(next.get("api_name"),"marketing_user_account")) {
                        treeMapIterator.remove();
                    }
                }
                List<List<String>> components = (List<List<String>>) it.get("components");
                Iterator<List<String>> listIterator = components.iterator();
                while (listIterator.hasNext()) {
                    List<String> stringList = listIterator.next();
                    if (stringList.size() == 1 && Objects.equals(stringList.get(0),"marketing_user_account")) {
                        listIterator.remove();
                    }
                }

            }
            if (Objects.equals(it.get("api_name"),"marketing_user_account")) {
                iterator.remove();
            }
        }
        System.out.println(getLayout);
        //更新layout
        UpdateLayoutArg updateArg = new UpdateLayoutArg();
        updateArg.setLayout_data(GsonUtil.toJson(getLayout));
        InnerResult<Map<String, Object>> mapInnerResult = objectLayoutService.updateLayout(HeaderObj.newInstance(88146, -10000), updateArg);
        System.out.println(mapInnerResult);
    }

    @Test
    public void findLayout() {
        FindLayoutArg arg = new FindLayoutArg();
        arg.setObjectDescribeApiName("EnterpriseInfoObj");
        arg.setApiName("EnterpriseInfoObj_layout_generate_by_UDObjectServer__c");
        InnerResult<FindLayoutResult> layout = objectLayoutService.findLayout(HeaderObj.newInstance(88146, -10000), arg);
        FindLayoutResult result = layout.getResult();
        LayoutDescribe getLayout = result.getLayout();
        List<LinkedTreeMap> componentsList = (List<LinkedTreeMap>) getLayout.get("components");
        for (LinkedTreeMap it : componentsList) {
            if (Objects.equals(it.get("type"),"tabs")) {
                List<LinkedTreeMap> tabsMap = (List<LinkedTreeMap>) it.get("tabs");
                LinkedTreeMap map = new LinkedTreeMap();
                map.put("api_name","marketing_user_account");
                map.put("header","营销用户");
                tabsMap.add(map);
                List<List<String>> components = (List<List<String>>) it.get("components");
                List<String> userMarketing = new ArrayList<>();
                userMarketing.add("marketing_user_account");
                components.add(userMarketing);
            }
        }
        LinkedTreeMap componentUserMap = new LinkedTreeMap();
        componentUserMap.put("field_section",new ArrayList<>());
        componentUserMap.put("buttons",new ArrayList<>());
        componentUserMap.put("api_name","marketing_user_account");
        componentUserMap.put("related_list_name","");
        componentUserMap.put("header","营销用户");
        componentUserMap.put("style", new HashMap<String, String>() {{
            put("height", "500px");
        }});
        componentUserMap.put("type","marketing_user_account");
        componentUserMap.put("moreUrl", "");
        componentUserMap.put("isSticky", false);
        //componentUserMap.put("grayLimit", 1);
        componentsList.add(componentUserMap);
        System.out.println(getLayout);
        //更新layout
        UpdateLayoutArg updateArg = new UpdateLayoutArg();
        updateArg.setLayout_data(GsonUtil.toJson(getLayout));
        InnerResult<Map<String, Object>> mapInnerResult = objectLayoutService.updateLayout(HeaderObj.newInstance(88146, -10000), updateArg);
        System.out.println(mapInnerResult);
    }

    @Test
    public void queryUserMarketingByEnterpriseInfo(){
        GetPageUserMarketingAccountByObjectArg arg = new GetPageUserMarketingAccountByObjectArg();
        arg.setCrmObjectApiName("AccountObj");
        arg.setCrmObjectId("64a78833f45506000169c5a3");
        arg.setPageNo(1);
        arg.setPageSize(5);
        Result<PageResult<UserMarketingAccountDetailsResult>> listResult = userMarketingAccountService.queryMarketingUserAccountByObject("88146", 1000, arg);
        System.out.println(listResult);
    }

    @Test
    public void queryCouponConfig(){
        EmployeeMsgResult result = new EmployeeMsgResult();
        result.setSourceCouponEnabled(false);
        marketingPluginConfigManager.handleCouponConfig("88146",result);
    }

    @Test
    public void test(){
        Result result = marketingUserPluginService.checkUnable("zhenju0111");
        if (result.isSuccess()) {
            marketingPluginConfigDAO.updatePluginStatus("zhenju0111",56,false);
        }
    }

    @Test
    public void test2(){
        marketingUserPluginService.addMarketingUserLayout("88146","AccountObj","AccountObj_layout_generate_by_UDObjectServer__c");
        marketingUserPluginService.addMarketingUserLayout("88146","AccountObj","AccountObj_layout_generate_by_UDObjectServer__c");
        System.out.println(1);
    }

}
