package com.facishare.marketing.provider.manager;

import com.facishare.mankeep.common.util.JsoupUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Map;

@Slf4j
public class AiZhanManagerTest extends BaseTest {

    @Autowired
    private AiZhanManager aiZhanManager;

    @Test
    public void testSiteRanking() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domain", "www.fxiaoke.com");
        map.put("startdate", "2022-10-05");
        map.put("enddate", "2022-12-05");
        Object o = aiZhanManager.siteRanking(map);
        log.warn("result:{}", o);
    }
    @Test
    public void siteRecordInformation() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domains", "www.fxiaoke.com");
        Object o = aiZhanManager.siteRecordInformation(map);
        log.warn("result:{}", o);
    }
    @Test
    public void keywordPcRanking() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domain", "www.fxiaoke.com");
        map.put("page", "1");
        Object o = aiZhanManager.keywordPcRanking(map);
        log.warn("result:{}", o);
    }
    @Test
    public void keywordMobilRanking() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domain", "www.fxiaoke.com");
        map.put("page", "1");
        Object o = aiZhanManager.keywordMobilRanking(map);
        log.warn("result:{}", o);
    }
    @Test
    public void keywordPcEmulationRanking() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domain", "www.fxiaoke.com");
        map.put("ranktype", "rankup");
        map.put("date", DateUtil.format2(new Date(new Date().getTime()-1000L*60*60*24*5)));
        map.put("wordpages", "0");
        map.put("page", "1");
        Object o = aiZhanManager.keywordPcEmulationRanking(map);
        log.warn("result:{}", o);
    }

    @Test
    public void keywordMobileEmulationRanking() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domain", "www.fxiaoke.com");
        map.put("ranktype", "rankdown");
        map.put("date", DateUtil.format2(new Date(new Date().getTime()-1000L*60*60*24*6)));
        map.put("wordpages", "3");
        map.put("page", "1");
        Object o = aiZhanManager.keywordMobileEmulationRanking(map);
        log.warn("result:{}", o);
    }


    @Test
    public void siteInclusionTrend() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domain", "www.fxiaoke.com");
        map.put("startdate",DateUtil.format2(new Date(new Date().getTime()-1000L*60*60*24*30*2)));
        map.put("enddate", DateUtil.format2(new Date(new Date().getTime()-1000L*60*60*24*30)));
        Object o = aiZhanManager.siteInclusionTrend(map);
        log.warn("result:{}", o);
    }
    @Test
    public void siteAntiChain() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domain", "www.fxiaoke.com");
        map.put("type", "home");//[home|pages]
        map.put("page", "1");
        Object o = aiZhanManager.siteAntiChain(map);
        log.warn("result:{}", o);
    }

    @Test
    public void siteAntiChainTrend() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domain", "www.fxiaoke.com");
        Object o = aiZhanManager.siteAntiChainTrend(map);
        log.warn("result:{}", o);
    }

    @Test
    public void getUrl() throws Exception {
//        Document document = JsoupUtil.getDocument("https://www.sharecrm.com/");
//        log.warn("result:{}", document.html());
        String o = aiZhanManager.getUrl("m.gaoding.com");
        log.warn("result:{}", o);
    }


    @Test
    public void seoHistoryData() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("domain", "www.fxiaoke.com");
        map.put("startdate",DateUtil.getPreviousMonth(new Date(), 3));
        map.put("enddate", DateUtil.format2(new Date()));
        Object o = aiZhanManager.seoHistoryData(map);
        log.warn("result:{}", o);
    }

}