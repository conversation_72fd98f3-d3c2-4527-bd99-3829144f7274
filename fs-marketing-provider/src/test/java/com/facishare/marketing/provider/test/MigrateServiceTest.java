package com.facishare.marketing.provider.test;

import com.facishare.marketing.api.arg.MigrateEasArg;
import com.facishare.marketing.api.service.MigrateService;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.manager.UserRoleManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatFriendsRecordObjDescribeManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.*;
import com.facishare.marketing.script.manager.PaasDataManager;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class MigrateServiceTest extends BaseTest{
    @Autowired
    private MigrateService migrateService;

    /**
     * 新增营销活动推广类型
     */
    @Test
    public void updateMarketingActivitySpreadType(){
        MigrateEasArg migrateEasArg = new MigrateEasArg();
        migrateEasArg.setInitAll(true);
        migrateService.updateMarketingActivitySpreadType(migrateEasArg);
    }

    @Autowired
    PaasDataManager paasDataManager;

    @Test
    public void testfix(){
        paasDataManager.fixCrmObjectData("74164", "LeadsObj", "biz_leads");
    }

    @Test
    public void testDelete(){
        paasDataManager.deleteWechatFriendsRecordObjByEa("83668");
    }

    @Autowired
    private WechatFriendsRecordObjDescribeManager wechatFriendsRecordObjDescribeManager;
    @Autowired
    private UserRoleManager userRoleManager;

    @Test
    public void testSync() {
        wechatFriendsRecordObjDescribeManager.syncExternalcontactData("74164");
    }

    @Test
    public void testAddQrCode() {
        wechatFriendsRecordObjDescribeManager.appendWechatFriendsRecordData("83668");
    }

    @Autowired
    private ContentPropagationDetailObjManager contentPropagationDetailObjManager;

    @Autowired
    private EmployeePromoteDetailObjManager employeePromoteDetailObjManager;

    @Test
    public void test() {
        userRoleManager.initObjPrivilege("74164", CrmObjectApiNameEnum.WECHAT_ACCOUNT_STATISTICS_OBJ.getName());
        userRoleManager.initObjPrivilege("74164", CrmObjectApiNameEnum.WECHAT_ACCOUNT_GROUP_STATISTICS_OBJ.getName());
    }

    @Test
    public void invokeTest() {
        MigrateEasArg arg = new MigrateEasArg();
        arg.setInitGrayEa(true);
        migrateService.invoke(arg);
    }
}
