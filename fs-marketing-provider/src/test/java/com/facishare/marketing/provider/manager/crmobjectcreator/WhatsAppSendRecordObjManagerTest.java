package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.provider.remote.FieldDescribeService;
import com.facishare.marketing.provider.remote.rest.arg.CustomFieldArg;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.result.Result;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


public class WhatsAppSendRecordObjManagerTest extends BaseTest {

    @Autowired
    private WhatsAppSendRecordObjManager whatsAppSendRecordObjManager;
    @Autowired
    private FieldDescribeService fieldDescribeService;
    @Autowired
    private EIEAConverter eieaConverter;

    @Test
    public void testCreateDescribe() {
        ObjectDescribe describe = whatsAppSendRecordObjManager.getOrCreateWhatsAppSendRecordObjDescribe("88273");
        System.out.println(describe);
    }

    @Test
    public void tryUpdateSengStatus() {
        whatsAppSendRecordObjManager.tryUpdateSendStatus("88146",  null, "*************", false);
        System.out.println(1);
    }

    @Test
    public void tryUpdateCustomFieldLabel1() {
        String ea = "88146";
        CustomFieldArg arg = new CustomFieldArg();
        arg.setDescribeAPIName("WhatsAppSendRecordObj");
        arg.setFieldApiName("field_rKsy8");
        Result rst = fieldDescribeService.disableCustomField(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        if (rst.isSuccess()) {
            Result result = fieldDescribeService.deleteDescribeCustomField(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), arg);
        }
        System.out.println(1);
        whatsAppSendRecordObjManager.tryAddErrorCodeFileToObj(ea);
        System.out.println(2);
        testCreateDescribe();
    }
}