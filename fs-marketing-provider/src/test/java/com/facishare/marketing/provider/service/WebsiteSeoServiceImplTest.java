package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.arg.aizhan.KeyWordRankArg;
import com.facishare.marketing.api.arg.aizhan.TdkDataArg;
import com.facishare.marketing.api.arg.aizhan.WebsiteBasicInfoArg;
import com.facishare.marketing.api.result.aizhan.*;
import com.facishare.marketing.api.service.WebsiteSeoService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Map;

public class WebsiteSeoServiceImplTest extends BaseTest {

    @Autowired
    private WebsiteSeoService websiteSeoService;

    @Test
    public void seoInit() {
        WebsiteBasicInfoArg arg = new WebsiteBasicInfoArg();
        arg.setId("b5bdf6b89b4d42789a008b606bef14e8");
        arg.setEa("88146");
        websiteSeoService.seoInit(arg);
        System.out.println("1");
    }

    @Test
    public void testQueryWebsiteBasicInfo() {
        WebsiteBasicInfoArg arg = new WebsiteBasicInfoArg();
        arg.setId("b5bdf6b89b4d42789a008b606bef14e8");
        arg.setEa("88146");
        arg.setStartTime(new Date().getTime()-3600L*24*1000*6);
        arg.setEndTime(new Date().getTime()-3600L*24*1000*3);
        Result<WebsiteBasicInfoResult> result = websiteSeoService.queryWebsiteBasicInfo(arg);
        System.out.println(result);
    }

    @Test
    public void gueryKeyWordRankArg() {
        KeyWordRankArg arg = new KeyWordRankArg();
        arg.setId("b5bdf6b89b4d42789a008b606bef14e8");
        arg.setEa("88146");
        Result<KeyWordRankResult> keyWordRankResultResult = websiteSeoService.queryKeyWordRankArg(arg);
        System.out.println("keyWordRankResultResult");
    }

    @Test
    public void querySeoPage() {
        WebsiteBasicInfoArg arg = new WebsiteBasicInfoArg();
        arg.setId("b5bdf6b89b4d42789a008b606bef14e8");
        arg.setEa("88146");
        Result<SeoPageResult> seoPageResultResult = websiteSeoService.querySeoPage(arg);
        System.out.println(seoPageResultResult);
    }

    @Test
    public void getDomainContent() {
        WebsiteBasicInfoArg arg = new WebsiteBasicInfoArg();
        arg.setId("20236b07b9044b6b8b1bcee5dc3eb2ad");
        arg.setEa("88146");
        Result<DomainContentResult> result = websiteSeoService.getDomainContent(arg);
        System.out.println(result);
    }

    @Test
    public void updateTdkData() {
        TdkDataArg arg = new TdkDataArg();
        arg.setId("b5bdf6b89b4d42789a008b606bef14e8");
        arg.setEa("88146");
        arg.setRating("1");
        arg.setHtmlContent("2");
        arg.setAiKeyword("3");
        websiteSeoService.updateTdkData(arg);
        System.out.println("result");
    }

    @Test
    public void queryTdkData() {
        WebsiteBasicInfoArg arg = new WebsiteBasicInfoArg();
        arg.setId("b5bdf6b89b4d42789a008b606bef14e8");
        arg.setEa("88146");
        Result<TdkDataResult> tdkDataResultResult = websiteSeoService.queryTdkData(arg);
        System.out.println(tdkDataResultResult);
    }

    @Test
    public void getAiTemplate() {

        Result<Map<String, String>> result = websiteSeoService.getAiTemplate();
        System.out.println(result);
    }

}