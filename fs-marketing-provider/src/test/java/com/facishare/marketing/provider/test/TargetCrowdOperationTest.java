/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test;

import com.facishare.marketing.api.arg.contentmarketing.ListContentMarketingEventArg;
import com.facishare.marketing.api.arg.crowd.MarketingCrowdPlanArg;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.service.crowd.TargetCrowdOperationService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.crowd.dao.MarketingEventTimedTaskEntityDao;
import com.facishare.marketing.provider.crowd.entity.MarketingEventTimedTaskEntity;
import com.facishare.marketing.provider.crowd.manager.TargetCrowdOperationManager;
import com.facishare.marketing.provider.remote.MarketingEventRemoteManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@Slf4j
public class TargetCrowdOperationTest extends BaseTest {

    @Autowired
    private TargetCrowdOperationManager targetCrowdOperationManager;
    @Autowired
    private MarketingEventRemoteManager marketingEventRemoteManager;
    @Autowired
    private MarketingEventTimedTaskEntityDao marketingEventTimedTaskEntityDao;
    @Autowired
    private TargetCrowdOperationService targetCrowdOperationService;

    @Test
    public void addDefaultEventTypeOptionsToFieldDescribe() {
        marketingEventRemoteManager.addDefaultEventTypeOptionsToFieldDescribe(88146, 1000);
    }

    @Test
    public void saveMarketingCrowdPlanTest() {
        long start = new Date().getTime();
        long end = DateUtil.plusDay(new Date(), 10).getTime();
        MarketingCrowdPlanArg arg = new MarketingCrowdPlanArg();
        arg.setName("周期人群测试0728-2");
        arg.setPlanType("periodicity");
        MarketingCrowdPlanArg.TimedTaskSetting timedTaskSetting = new MarketingCrowdPlanArg.TimedTaskSetting();
        timedTaskSetting.setRepeatRangeStart(start);
        timedTaskSetting.setRepeatRangeEnd(end);
        timedTaskSetting.setRepeatType(1);
        timedTaskSetting.setRepeatValue(Lists.newArrayList(1, 2, 3));
        timedTaskSetting.setTriggerAtMinutes(1400);
        timedTaskSetting.setSinglePeriod(3);
        timedTaskSetting.setJoinLimit(1);
        arg.setTimedTaskSetting(timedTaskSetting);
        arg.setMarketingUserGroupId("d61f8c3044a04892bcf26734586ba071");
        ObjectData marketingEvent = new ObjectData();
        marketingEvent.setName("周期人群测试0728-2");
        marketingEvent.put("begin_time", start);
        marketingEvent.put("end_time", end);
        marketingEvent.put("event_type", "periodicity");
        marketingEvent.put("owner", Lists.newArrayList("1000"));
        marketingEvent.put("data_own_organization", Lists.newArrayList("999999"));
        arg.setMarketingEvent(marketingEvent);
        targetCrowdOperationManager.saveMarketingCrowdPlan("88146", 1000, arg);
    }

    @Test
    public void handleTimedTaskTest() {
        MarketingEventTimedTaskEntity task = marketingEventTimedTaskEntityDao.getById("a6f2df2758dd4938bdb21fa0c449cc7c");
//        targetCrowdOperationManager.handleTimedTask(task);
//        targetCrowdOperationManager.batchAddMaterialToChildMarketingEvent("88146", 1000, "64cb1fb34a5c540001efbc39", "64d0cbb50b9834000168d7b9");
//        targetCrowdOperationManager.batchAddSopToChildMarketingEvent("88146", 1000, "64cb1fb34a5c540001efbc39", "64d0cbb50b9834000168d7b9");
        targetCrowdOperationManager.batchUpdateSopRelation("88146", 1000, "65977b8b2d49e20007707e52", "65977c6f2d49e2000770e204");
    }

    @Test
    public void getListTest() {
        ListContentMarketingEventArg arg = new ListContentMarketingEventArg();
        arg.setPageNo(1);
        arg.setPageSize(20);
        Result<PageResult<MarketingEventsBriefResult>> result = targetCrowdOperationService.listCrowdMarketingEvent("88146", 1000, arg);
        log.info("=== result : {}", GsonUtil.toJson(result));
    }

}
