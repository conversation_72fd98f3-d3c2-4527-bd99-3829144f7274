package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;

import static org.junit.Assert.*;

@Slf4j
public class MarketingContentObjManagerTest extends BaseTest {
    @Autowired
    private MarketingContentObjManager marketingContentObjManager;
    @Autowired
    private CrmV2Manager crmV2Manager;

    @Test
    public void initOtherObj() {
        String ea= "zhenju0111";
        marketingContentObjManager.initOtherObj(ea);
        log.info("initOtherObj success");
    }

    @Test
    public void getObjDetail() {
        String ea= "88146";
        marketingContentObjManager.tryUpdateMarketingActivityReferenceForMarketingContent(ea);
        log.info("initOtherObj success:",1);
    }
    @Test
    public void assignRecord() {
        String ea= "88146";
        marketingContentObjManager.assignRecord(ea);
        log.info("initOtherObj success:");
    }
    @Test
    public void editLayoutInit() {
        String ea= "88146";
        marketingContentObjManager.addEditLayoutConfByEa(ea);
        log.info("initOtherObj success:");
    }
    @Test
    public void addEditLayoutConfByEa() {
        String ea= "zhenju0111";
        marketingContentObjManager.createEditPageLayoutRule(ea);
        log.info("initOtherObj success:");
    }

    @Test
    public void handleMarketingContentChangeEvent() {
        String ea= "88146";
        String objId = "67297d243592f8000109e69f";
        marketingContentObjManager.handleMarketingContentChangeEvent(ea,objId,"i",false);
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("_id", objId);
        dataMap.put("current_status","failed");
        crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.MARKETING_CONTENT_OBJ.getName(), dataMap);
        log.info("initOtherObj success:");
    }


}