package com.facishare.marketing.provider.service.sdr;

import com.facishare.marketing.api.arg.sdr.SdrActionArg;
import com.facishare.marketing.api.service.sdr.SdrActionService;
import com.facishare.marketing.common.result.FunctionResult;
import com.facishare.marketing.provider.test.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class SdrActionServiceTest extends BaseTest {

    @Autowired
    private SdrActionService sdrActionService;

//    @Test
//    public void invoke() {
//        SdrActionArg arg = new SdrActionArg();
//        arg.setTenantId(88146);
//        arg.setFsUserId(1000);
//        arg.setBizName("convertUserMarketing");
//        arg.setJsonParams("{\"visitorId\":\"6822f31c5f1df600018bbe59\"}");
//        FunctionResult<Map<String, Object>> invoke = sdrActionService.invoke(arg);
//        System.out.println(invoke);
//    }

    @Test
    public void test() {
        SdrActionArg arg = new SdrActionArg();
        arg.setVisitorId("686f5a4616549e0007d2193d");
        arg.setCustomerSessionId("2554777309200515345");
        arg.setTenantId(88146);
        arg.setFsUserId(1000);
        FunctionResult<Map<String, Object>> mapFunctionResult = sdrActionService.marketingInsights(arg);
        System.out.println("yes:" + mapFunctionResult);
    }

}