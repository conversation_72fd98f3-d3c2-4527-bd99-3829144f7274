package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.arg.GetMarketingEventDetailArg;
import com.facishare.marketing.api.arg.RelationConfig;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.service.TraceabilityRelationService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

@Slf4j
public class MarketingEventServiceTest extends BaseTest {
    @Autowired
    private MarketingEventService marketingEventService;


    @Test
    public void createEventWorkspaceObj() {
        GetMarketingEventDetailArg arg = new GetMarketingEventDetailArg();
        arg.setEa("88146");
        arg.setFsUserId(1000);
        arg.setMarketingEventId("6887536fcfd817000108a5bc");
        Result<String> result = marketingEventService.createEventWorkspaceObj(arg);
        System.out.println(result);
    }

}