package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.arg.AddMaterialsArg;
import com.facishare.marketing.api.arg.AddUserGroupToMarketingEventArg;
import com.facishare.marketing.api.arg.ListBriefMarketingEventsArg;
import com.facishare.marketing.api.arg.ListMaterialsArg;
import com.facishare.marketing.api.arg.marketingEvent.GetMarketingEventByObjectIdArg;
import com.facishare.marketing.api.data.MarketingActivityData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.vo.QueryMarketingEventLevelDataVO;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import shade.com.google.gson.Gson;

import java.util.List;


/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/17.
 */
@Slf4j
public class MarketingEventServiceTest  extends BaseTest {
    @Autowired
    private MarketingEventService marketingEventService;

    @Test
    public void listBriefMarketingEvents(){
        ListBriefMarketingEventsArg arg = new ListBriefMarketingEventsArg();
    //    arg.setBegin(1572537600000L);
    //    arg.setEnd(1575129599999L);
        Result result = marketingEventService.listBriefMarketingEvents("74164",1000,arg);
        log.info("== result:{}",result);
    }
    @Test
    public void getMarketingEventsDetail(){
        Result result = marketingEventService.getMarketingEventsDetail("88146",1000,"6887536fcfd817000108a5bc");
        log.info("== result:{}",result);
    }
    @Test
    public void mergeUserGroupToMarketingEvent(){
        AddUserGroupToMarketingEventArg arg = new AddUserGroupToMarketingEventArg();
        arg.setUserGroupIds(Lists.newArrayList("06ee2b9a6b9a4ae2acc2b3eaf7e3bb07"));
        arg.setMarketingEventId("5d3186c3b3bcaa000191cc64");
        Result result = marketingEventService.mergeUserGroupToMarketingEvent("2",1000,arg);
        log.info("== result:{}",result);
    }
    @Test
    public void listMaterials(){
        ListMaterialsArg arg = new ListMaterialsArg();
        arg.setId("5dedf031c573230001884138");
        arg.setPageNum(1);
        arg.setPageSize(1000);
        PageResult<AbstractMaterialData> result = marketingEventService.listMaterials("74164", -10000, arg);
        log.info("== result:{}",result);
    }

    @Test
    public void checkAndAddMaterials(){
        AddMaterialsArg arg = GsonUtil.fromJsonSerializingNull("{\"marketingEventId\":\"5d550af37cfed907e531bf38\",\"materialInfos\":[{\"objectType\":6,\"objectId\":\"aa88530110d64f95b2f13ffe44326056\"}]}",AddMaterialsArg.class);
        Result<Boolean> result = marketingEventService.checkAndAddMaterials("74164",-10000, arg);
        log.info("== result:{}",result);
    }

    @Test
    public void listUserGroupFromMarketingEvent(){
        Result result = marketingEventService.listUserGroupFromMarketingEvent("2",-10000,"5c666bf4723781000183d62d");
        log.info("== result:{}",result);
    }
    @Test
    public void deleteRelation(){
        Result result = marketingEventService.deleteRelation("2",-10000,"5d3186c3b3bcaa000191cc64",4,"1ac6f3f0d3fc48b1977357fd4dbc5263");
        log.info("== result:{}",result);
    }
    @Test
    public void getOrCreateEventTypeAndColorConfig(){
        Result result = marketingEventService.getOrCreateEventTypeAndColorConfig("2",-10000);
        log.info("== result:{}",result);
    }

    @Test
    public void getMarketingEventByObjectIdTest() {
        GetMarketingEventByObjectIdArg arg = new GetMarketingEventByObjectIdArg();
        arg.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
        arg.setObjectId("661a5036cb4a4baca8ecaedbb672d8e3");
        arg.setPageNum(1);
        arg.setPageSize(100);
        marketingEventService.getMarketingEventByObjectId("74164", 1000, arg);
    }

    @Test
    public void queryMarketingEventLevelData() {
        QueryMarketingEventLevelDataVO vo = new QueryMarketingEventLevelDataVO();
        vo.setEa("74164");
        vo.setMarketingEventId("60ec12af91c2e9000151176a");
        System.out.println(marketingEventService.queryMarketingEventLevelData(vo));
    }

}
