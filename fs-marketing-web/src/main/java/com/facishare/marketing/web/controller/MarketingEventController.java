package com.facishare.marketing.web.controller;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.MarketingEventSetConfigArg.EventTypeAndColorData;
import com.facishare.marketing.api.arg.marketingEvent.*;
import com.facishare.marketing.api.data.MarketingUserGroupData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.result.MarketingEventDetailResult;
import com.facishare.marketing.api.result.PayOrderResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsResult;
import com.facishare.marketing.api.result.marketingEvent.*;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.vo.QueryMarketingEventLevelDataVO;
import com.facishare.marketing.api.vo.marketingevent.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.marketingevent.*;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/15.
 */
@RestController
@RequestMapping("marketingEvent")
@Slf4j
@Api(description = "营销日历", tags = "MarketingEventController")
public class MarketingEventController {
    @Autowired
    private MarketingEventService marketingEventService;

    @CheckIdentityTrigger
    @RequestMapping(value = "getMarketingEventsDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取市场活动详细信息", notes = "获取市场活动详细信息",tags = "322")
    public Result<MarketingEventsResult> getMarketingEventsDetail(@RequestBody IdArg arg) {
        return marketingEventService.getMarketingEventsDetail(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "addMaterial", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "添加素材", notes = "添加素材关联")
    public Result<String> addMaterial(@RequestBody AddMaterialArg arg) {
        return marketingEventService.addMaterial(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "deleteRelation", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "删除素材关联", notes = "删除素材关联", tags = "322")
    public Result deleteRelation(@RequestBody DeleteRelationArg arg) {
        return marketingEventService.deleteRelation(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getMarketingEventId(), arg.getObjectType(), arg.getObjectId());
    }
    
    @RequestMapping(value = "updateIsApplyObject", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置是否报名物料", notes = "设置是否报名物料", tags = "570")
    public Result<Void> updateIsApplyObject(@RequestBody UpdateIsApplyObjectArg arg) {
        return marketingEventService.updateIsApplyObject(arg.getId(), arg.getIsApplyObject());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "listMaterials", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "拉取素材 文章 6 产品 4 二维码海报 24  表单 16", notes = "拉取素材")
    public Result<PageResult<AbstractMaterialData>> listMaterials(@RequestBody ListMaterialsArg arg) {
        return Result.newSuccess(marketingEventService.listMaterials(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg));
    }

    @RequestMapping(value = "listMaterialsByMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "拉取素材 文章 6 产品 4 二维码海报 24  表单 16  微页面 26", notes = "拉取素材")
    public Result<PageResult<AbstractMaterialData>> listMaterialsByMarketingEvent(@RequestBody ListMaterialsByMarketingEventArg arg) {
        return Result.newSuccess(marketingEventService.listMaterialsByMarketingEvent(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg));
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "mergeUserGroupToMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "关联市场活动和营销用户群组", notes = "关联市场活动和营销用户群组")
    public Result addUserGroupToMarketingEvent(@RequestBody AddUserGroupToMarketingEventArg arg) {
        return marketingEventService.mergeUserGroupToMarketingEvent(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "listUserGroupFromMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取市场活动和营销用户群组关联", notes = "获取市场活动和营销用户群组关联")
    public Result<List<MarketingUserGroupData>> listUserGroupFromMarketingEvent(@RequestBody IdArg arg) {
        return marketingEventService.listUserGroupFromMarketingEvent(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "listBriefMarketingEvents", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取每月简略的市场活动信息（带权限，只能查看到当前人员所能看到的全部市场活动）", notes = "获取每月简略的市场活动信息", tags = {"3.1.2","322"})
    public Result<List<MarketingEventsBriefResult>> listBriefMarketingEvents(@RequestBody ListBriefMarketingEventsArg arg) {
        arg.setQueryLimitCount(2000);
        return marketingEventService.listBriefMarketingEvents(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "setEventTypeAndColorConfig", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置市场活动日历颜色", notes = "设置市场活动日历颜色", tags = "3.1.2")
    public Result<Boolean> setEventTypeAndColorConfig(@RequestBody MarketingEventSetConfigArg arg) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(arg.getEventTypeAndColorDataList()));
        return marketingEventService.setEventTypeAndColorConfig(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(),arg.getEventTypeAndColorDataList());
    }

    @RequestMapping(value = "getOrCreateEventTypeAndColorConfig", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取市场活动日历颜色，没有则创建默认配置", notes = "获取市场活动日历颜色，没有则创建默认配置", tags = "3.1.2")
    public Result<List<EventTypeAndColorData>> getOrCreateEventTypeAndColorConfig() {
        return marketingEventService.getOrCreateEventTypeAndColorConfig(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @RequestMapping(value = "getMarketingEventByObjectId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取指定物料关联的所有市场活动", notes = "获取指定物料关联的所有市场活动", tags = "3.2.2")
    public Result<PageResult<GetMarketingEventByObjectIdResult>> getMarketingEventByObjectId(@RequestBody GetMarketingEventByObjectIdArg arg) {
        return marketingEventService.getMarketingEventByObjectId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }
    
    @RequestMapping(value = "listPayOrdersByCampaignId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据合并表ID获取支付订单列表", tags = "620")
    public Result<List<PayOrderResult>> listPayOrdersByCampaignId(@RequestBody ListPayOrdersByCampaignIdArg arg) {
        return marketingEventService.listPayOrdersByCampaignId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }
    

    @RequestMapping(value = "queryMarketingEventLevelData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取市场活动层级数据")
    public Result<QueryMarketingEventLevelDataResult> queryMarketingEventLevelData(@RequestBody QueryMarketingEventLevelDataArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryMarketingEventLevelDataVO vo = BeanUtil.copy(arg, QueryMarketingEventLevelDataVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        return marketingEventService.queryMarketingEventLevelData(vo);
    }

    @RequestMapping(value = "relateSubMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "多会场关联子活动", notes = "多会场关联子活动")
    public Result<Void> relateSubMarketingEvent(@RequestBody RelateSubMarketingEventArg arg) {
        Result checkResult = arg.validateParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        RelateSubMarketingEventVO vo = BeanUtil.copy(arg, RelateSubMarketingEventVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingEventService.relateSubMarketingEvent(vo);
    }

    @RequestMapping(value = "unRelateSubMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "解除绑定", notes = "解除绑定")
    public Result<Void> unRelateSubMarketingEvent(@RequestBody UnRelateSubMarketingEventArg arg) {
        Result checkResult = arg.validateParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        UnRelateSubMarketingEventVO vo = BeanUtil.copy(arg, UnRelateSubMarketingEventVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingEventService.unRelateSubMarketingEvent(vo);
    }

    @RequestMapping(value = "getSubMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询多会场关联子活动", notes = "查询多会场关联子活动")
    public Result<List<MarketingEventSimpleResult>> getSubMarketingEvent(@RequestBody GetSubMarketingEventArg arg) {
        Result checkResult = arg.validateParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        GetSubMarketingEventVO vo = BeanUtil.copy(arg, GetSubMarketingEventVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingEventService.getSubMarketingEvent(vo);
    }

    @RequestMapping(value = "getMultiVenueMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "多会场活动详情", notes = "多会场活动详情")
    public Result<MultiVenueMarketingEventResult> getMultiVenueMarketingEvent(@RequestBody GetMultiVenueMarketingEventArg arg) {
        Result checkResult = arg.validateParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        GetMultiVenueMarketingEventVO vo = BeanUtil.copy(arg, GetMultiVenueMarketingEventVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingEventService.getMultiVenueMarketingEvent(vo);
    }

    @RequestMapping(value = "syncDataToSubMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "同步报名数据到子活动", notes = "同步报名数据到子活动")
    public Result<Void> syncDataToSubMarketingEvent(@RequestBody SyncDataToSubMarketingEventArg arg) {
        Result checkResult = arg.validateParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        SyncDataToSubMarketingEventVO vo = BeanUtil.copy(arg, SyncDataToSubMarketingEventVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingEventService.syncDataToSubMarketingEvent(vo);
    }

    @RequestMapping(value = "getSyncRules", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询同步规则", notes = "查询同步规则")
    public Result<List<SaveSyncRulesArg.SyncRule>> getSyncRules(@RequestBody IdArg arg) {
        if (StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR.getErrorCode(), "marketingEventId is null");
        }
        return marketingEventService.getSyncRules(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "saveSyncRules", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "保存同步规则", notes = "保存同步规则")
    public Result<Void> saveSyncRules(@RequestBody SaveSyncRulesArg arg) {
        Result checkResult = arg.validateParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        return marketingEventService.saveSyncRules(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "listMultiVenueMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "多会场活动列表", notes = "多会场活动列表")
    public Result<PageResult<MultiVenueMarketingEventListResult>> listMultiVenueMarketingEvent(@RequestBody ListMultiVenueMarketingEventArg arg) {
        if (!arg.isPageArgValid()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingEventService.listMultiVenueMarketingEvent(arg);
    }

    @RequestMapping(value = "getMarketingEventDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "活动详情", notes = "活动详情")
    public Result<MarketingEventDetailResult> getMarketingEventDetail(@RequestBody GetMarketingEventDetailArg arg) {
        if(StringUtils.isEmpty(arg.getEa())){
            arg.setEa(UserInfoKeeper.getEa());
        }
        if(StringUtils.isEmpty(arg.getEa()) || StringUtils.isEmpty(arg.getMarketingEventId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return marketingEventService.getMarketingEventsDetail(arg);
    }

    @RequestMapping(value = "createEventWorkspaceObj", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取或者创建活动工作空间", notes = "活动详情")
    public Result<String> createEventWorkspaceObj(@RequestBody GetMarketingEventDetailArg arg) {
        if(StringUtils.isEmpty(arg.getEa())){
            arg.setEa(UserInfoKeeper.getEa());
        }
        if(StringUtils.isEmpty(arg.getEa()) || StringUtils.isEmpty(arg.getMarketingEventId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingEventService.createEventWorkspaceObj(arg);
    }

    @RequestMapping(value = "syncEventWorkspaceAsset", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "同步市场活动工作资产", notes = "活动详情")
    public Result<String> syncEventWorkspaceAsset(@RequestBody SyncWorkspaceAssetArg arg) {
        if(StringUtils.isEmpty(arg.getEa())){
            arg.setEa(UserInfoKeeper.getEa());
        }
        if(StringUtils.isEmpty(arg.getEa()) || StringUtils.isEmpty(arg.getMarketingEventId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingEventService.syncEventWorkspaceAsset(arg);
    }
}
