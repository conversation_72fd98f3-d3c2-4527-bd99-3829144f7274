package com.facishare.marketing.web.arg.officialWebsite;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2021/01/13
 **/
@Data
public class QueryUtmCampaigLeadCountArg implements Serializable {

    private Integer pageNum;

    private Integer pageSize;

    @ApiModelProperty("官网id")
    private String id;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束时间")
    private Long endTime;

    public boolean isWrongParam() {
        return StringUtils.isBlank(id) || pageNum == null || pageSize == null;
    }

}
