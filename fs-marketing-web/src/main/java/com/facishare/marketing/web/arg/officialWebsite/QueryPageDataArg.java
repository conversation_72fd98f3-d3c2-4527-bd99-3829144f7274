package com.facishare.marketing.web.arg.officialWebsite;

import com.facishare.marketing.common.enums.officialWebsite.OfficialWebsiteStatusEnum;
import com.facishare.marketing.web.arg.BasePageArg;
import io.swagger.annotations.ApiModelProperty;
import java.util.Arrays;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created  By zhoux 2019/11/26
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryPageDataArg extends BasePageArg {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "状态 0 正常 1停用 2删除")
    private Integer status;

    public boolean isWrongParam() {
        return this.getPageNum() == null || this.getPageSize() == null || (status != null && Arrays.stream(OfficialWebsiteStatusEnum.values()).noneMatch(data -> data.getValue().equals(status)));
    }

}
