package com.facishare.marketing.web.arg.officialWebsite;

import com.facishare.marketing.common.enums.officialWebsite.OfficialWebsiteEventAttributesTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2020/08/19
 **/
@Data
public class QueryEventAttributesDetailArg implements Serializable {

    @ApiModelProperty("当前保存数据类型 0 事件 1属性")
    private Integer type;

    private Integer pageNum;

    private Integer pageSize;

    @ApiModelProperty("父级id 查询属性时必传")
    private String parentId;

    private String websiteId;


    public boolean isWrongParam() {
        return type == null || (type.equals(OfficialWebsiteEventAttributesTypeEnum.ATTRIBUTES.getType()) && StringUtils.isBlank(parentId)) || pageNum == null || pageSize == null;
    }



}
