package com.facishare.marketing.web.arg.officialWebsite;

import com.facishare.marketing.web.arg.BasePageArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2019/12/03
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryWebsiteCtaArg extends BasePageArg {
    @ApiModelProperty("官网id")
    private String id;

    public boolean isWrongParam() {
        return StringUtils.isBlank(id) || this.getPageNum() == null || this.getPageSize() == null;
    }

}
