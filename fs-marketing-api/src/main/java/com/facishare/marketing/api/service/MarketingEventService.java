package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.MarketingEventSetConfigArg.EventTypeAndColorData;
import com.facishare.marketing.api.arg.marketingEvent.GetMarketingEventByObjectIdArg;
import com.facishare.marketing.api.arg.marketingEvent.ListMultiVenueMarketingEventArg;
import com.facishare.marketing.api.arg.marketingEvent.SaveSyncRulesArg;
import com.facishare.marketing.api.data.MarketingUserGroupData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.result.MarketingEventDetailResult;
import com.facishare.marketing.api.result.PayOrderResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsResult;
import com.facishare.marketing.api.result.marketingEvent.*;
import com.facishare.marketing.api.vo.QueryMarketingEventLevelDataVO;
import com.facishare.marketing.api.vo.marketingevent.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import org.ini4j.spi.RegEscapeTool;

import java.util.List;
import java.util.Map;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/15.
 */
public interface MarketingEventService {
    Result<List<MarketingEventsBriefResult>> listBriefMarketingEvents(String ea, Integer fsUserId, ListBriefMarketingEventsArg arg);

    Result<MarketingEventsResult> getMarketingEventsDetail(String ea, Integer fsUserId, String marketingEventId);

    Result<String> addMaterial(String ea, Integer fsUserId, AddMaterialArg arg);

    Result<Boolean> checkAndAddMaterials(String ea, Integer fsUserId, AddMaterialsArg arg);

    PageResult<AbstractMaterialData> listMaterials(String ea, Integer fsUserId, ListMaterialsArg arg);

    Result mergeUserGroupToMarketingEvent(String ea, Integer fsUserId, AddUserGroupToMarketingEventArg arg);

    Result<List<MarketingUserGroupData>> listUserGroupFromMarketingEvent(String ea, Integer fsUserId, String marketingEventId);

    Result deleteRelation(String ea, Integer fsUserId, String marketingEventId, Integer objectType, String objectId);
    
    Result<Void> updateIsApplyObject(String relationId, Boolean isApplyObject);

    Result<Boolean> setEventTypeAndColorConfig(String ea, Integer fsUserId, List<MarketingEventSetConfigArg.EventTypeAndColorData> eventTypeAndColorDataList);

    Result<List<EventTypeAndColorData>> getOrCreateEventTypeAndColorConfig(String ea, Integer fsUserId);

    Result<PageResult<GetMarketingEventByObjectIdResult>> getMarketingEventByObjectId(String ea, Integer fsUserId, GetMarketingEventByObjectIdArg arg);

    PageResult<AbstractMaterialData> listMaterialsByMarketingEvent(String ea, Integer fsUserId, ListMaterialsByMarketingEventArg arg);
    
    Result<List<PayOrderResult>> listPayOrdersByCampaignId(String ea, Integer fsUserId, ListPayOrdersByCampaignIdArg arg);

    Result<QueryMarketingEventLevelDataResult> queryMarketingEventLevelData(QueryMarketingEventLevelDataVO vo);

    /**
     * 多会场关联子活动
     * @param vo
     * @return
     */
    Result<Void> relateSubMarketingEvent(RelateSubMarketingEventVO vo);

    /**
     * 多会场关联子活动
     * @param vo
     * @return
     */
    Result<Void> unRelateSubMarketingEvent(UnRelateSubMarketingEventVO vo);

    /**
     * 查询多会场关联子活动
     * @param vo
     * @return
     */
    Result<List<MarketingEventSimpleResult>> getSubMarketingEvent(GetSubMarketingEventVO vo);

    /**
     * 查询多会场关联子活动
     * @param vo
     * @return
     */
    Result<MultiVenueMarketingEventResult> getMultiVenueMarketingEvent(GetMultiVenueMarketingEventVO vo);

    /**
     * 同步报名数据到子活动
     * @param vo
     * @return
     */
    Result<Void> syncDataToSubMarketingEvent(SyncDataToSubMarketingEventVO vo);

    Result<Void> syncDataToTargetMarketingEventByRule();
    Result<Void> syncDataToTargetMarketingEventById(String ea, String marketingEventId, String campaignObjId);

    Result<List<SaveSyncRulesArg.SyncRule>> getSyncRules(String ea, Integer fsUserId, IdArg arg);
    Result<Void> saveSyncRules(String ea, Integer fsUserId, SaveSyncRulesArg arg);

    Result<PageResult<MultiVenueMarketingEventListResult>> listMultiVenueMarketingEvent(ListMultiVenueMarketingEventArg arg);

    Result<MarketingEventDetailResult> getMarketingEventsDetail(GetMarketingEventDetailArg arg);

    Result<String> createEventWorkspaceObj(GetMarketingEventDetailArg arg);

    Result<String> syncEventWorkspaceAsset(SyncWorkspaceAssetArg arg);
}
