package com.facishare.marketing.api.result;

import com.facishare.marketing.api.data.BoardStatisticData;
import com.facishare.marketing.api.data.BoardTemplateData;
import com.facishare.marketing.api.util.BoardDataConverter;
import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

/**
 * Author JuneHua
 * Date 2020/8/6 10:50
 * Version 1.0
 */
@Data
public class BoardResult implements Serializable {
    @ApiModelProperty(value = "看板ID")
    private String id;
    @ApiModelProperty(value = "看板名称")
    private String name;
    @ApiModelProperty("看板类型：（data：数据看板）、（customer：客户看板）、（project：项目看板）、（sop：SOP看板）")
    private String type;
    @ApiModelProperty(value = "看板的描述信息")
    private String description;
    @ApiModelProperty("看板封面")
    private String cover;
    @ApiModelProperty(value = "创建人id")
    private Integer creatorId;
    @ApiModelProperty(value = "看板创建时间")
    private Long createTime;
    @ApiModelProperty(value = "看板卡片列表")
    private List<BoardCardListResult> boardCardLists;
    @ApiModelProperty(value = "卡片列表版本")
    private Integer boardCardListVersion;
    @ApiModelProperty(value = "看板下的卡片总数")
    private Integer boardCardNum;
    @ApiModelProperty(value = "看板下的任务总数")
    private Integer boardCardTaskNum;
    @ApiModelProperty(value = "看板的可见范围")
    private String visibleRange;
    @ApiModelProperty(value = "可见范围的用户列表，当可见范围为private时有效")
    private List<Integer> boardUsers;
    @ApiModelProperty("模板的类型:0为非模板，默认类型  1 为企业模板")
    private Integer templateType;
    @ApiModelProperty("模板是否有更新、删除权限  为true时拥有权限，null或者false为无权限")
    private Boolean haveAuthority;
    @ApiModelProperty("目标对象名称，如市场活动名称、营销活动名称、营销计划名称、目标人群名称")
    private String targetObjectName;
    @ApiModelProperty("目标类型：uv用户数，pv浏览数，form_data_user_count表单线索数，spread_employee_count推广员工数，external_user_count企业微信客户数，group_user_count人群成员数，work_finished_count工作完成度")
    private String goalType;
    @ApiModelProperty("目标值")
    private Integer goalValue;
    @ApiModelProperty("目标完成情况")
    private BoardStatisticData statisticData;
    @ApiModelProperty(value = "营销人群ID列表(侧栏-关联人群)")
    private List<String> marketingUserGroupIds = new ArrayList<>(0);
    @ApiModelProperty("营销活动类型")
    private Integer marketingActivityType;
    @ApiModelProperty("营销活动ID")
    private String marketingActivityId;
    @ApiModelProperty("市场活动类型")
    private String marketingEventType;
    @ApiModelProperty("市场活动ID")
    private String marketingEventId;
    @ApiModelProperty("分销计划ID")
    private String distributePlanId;
    @ApiModelProperty("关联对象-目标人群ID")
    private String goalUserGroupId;
    /** @see com.facishare.marketing.common.enums.BoardAssociatedObjectTypeEnum */
    @ApiModelProperty("关联对象类型， common marketing_activity_data marketing_event_data external_user_data distribute_plan_data")
    private String associatedObjectType;
    /** @see com.facishare.marketing.common.enums.MarketingSceneType */
    @ApiModelProperty("【模板】应用场景：common通用场景、conference会议营销、live直播营销、marketing_event活动营销、advertising_marketing广告营销、wx_service_account公众号营销、email邮件营销、sms_marketing短信营销、distribute_plan社会化分销")
    private String sceneType;

    public BoardTemplateData toBoardTemplateData() {
        BoardTemplateData result = new BoardTemplateData();
        BeanUtils.copyProperties(this, result, "visibleRage", "type", "boardCardLists");
        if (CollectionUtils.isEmpty(boardCardLists)) {
            result.setBoardCardLists(new ArrayList<>());
        } else {
            List<BoardTemplateData.BoardCardListWithCardData> templateBoardCardLists = boardCardLists
                    .stream()
                    .filter(Objects::nonNull)
                    .map(BoardCardListResult::toBoardCardListWithCardData)
                    .collect(Collectors.toList());
            result.setBoardCardLists(templateBoardCardLists);
        }
        return result;
    }

    public void pushBoardCardResult(BoardCardResult boardCardResult) {
        Preconditions.checkState(boardCardLists != null);
        for (BoardCardListResult boardCardList : boardCardLists) {
            if (boardCardList.getId().equals(boardCardResult.getBoardCardListId())) {
                if (boardCardList.getBoardCards() == null) {
                    boardCardList.setBoardCards(new LinkedList<>());
                }
                boardCardList.getBoardCards().add(boardCardResult);
            }
        }
    }
    /**
     * 转换为运营任务格式
     * 格式：{"0":{"name":"分类标题A","boardCards":{"0":{"name":"任务内容A1"},"1":{"name":"任务内容A2"}}},"1":{"name":"分类标题B","boardCards":{"0":{"name":"任务内容B1"}}}}
     * @return 转换后的运营任务格式
     */
    public Map<String, BoardColumnData> toBoardColumnFormat() {
        return BoardDataConverter.convertToBoardColumnFormat(this);
    }

/*
    public void initTargetResult() {
        targetResult.setBoardId(id);
        if (targetResult.getMarketingUserGroupIds() == null || targetResult.getMarketingUserGroupIds().isEmpty()) {
            targetResult.setMarketingUserGroupIds(marketingUserGroupIds);
        }
        targetResult.setType(marketingType);
        targetResult.setGoalType(goalType);
        targetResult.setGoalValue(goalValue);
    }

 */
}
